<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

const canvasRef = ref<HTMLCanvasElement | null>(null);
let animationFrameId: number | null = null;

interface Particle {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  color: string;
}

const particles: Particle[] = [];
const particleCount = 50; // 粒子数量不要太多，保持简约
const connectionDistance = 100; // 连接线的最大距离
const colors = ['rgba(0, 168, 255, 0.5)', 'rgba(0, 198, 255, 0.5)', 'rgba(0, 255, 255, 0.5)'];

// 初始化粒子
function initParticles() {
  if (!canvasRef.value) return;
  
  const canvas = canvasRef.value;
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // 设置canvas尺寸为窗口大小
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
  
  // 创建粒子
  for (let i = 0; i < particleCount; i++) {
    particles.push({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      size: Math.random() * 2 + 1,
      speedX: (Math.random() - 0.5) * 0.5, // 速度不要太快
      speedY: (Math.random() - 0.5) * 0.5,
      color: colors[Math.floor(Math.random() * colors.length)]
    });
  }
  
  animate();
}

// 绘制粒子和连接线
function drawParticles() {
  if (!canvasRef.value) return;
  
  const canvas = canvasRef.value;
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  // 更新和绘制粒子
  particles.forEach((particle, index) => {
    // 更新位置
    particle.x += particle.speedX;
    particle.y += particle.speedY;
    
    // 边界检查
    if (particle.x < 0 || particle.x > canvas.width) {
      particle.speedX = -particle.speedX;
    }
    if (particle.y < 0 || particle.y > canvas.height) {
      particle.speedY = -particle.speedY;
    }
    
    // 绘制粒子
    ctx.beginPath();
    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
    ctx.fillStyle = particle.color;
    ctx.fill();
    
    // 绘制连接线
    for (let j = index + 1; j < particles.length; j++) {
      const dx = particles[j].x - particle.x;
      const dy = particles[j].y - particle.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      if (distance < connectionDistance) {
        ctx.beginPath();
        ctx.strokeStyle = `rgba(0, 168, 255, ${0.2 * (1 - distance / connectionDistance)})`;
        ctx.lineWidth = 0.5;
        ctx.moveTo(particle.x, particle.y);
        ctx.lineTo(particles[j].x, particles[j].y);
        ctx.stroke();
      }
    }
  });
}

// 动画循环
function animate() {
  drawParticles();
  animationFrameId = requestAnimationFrame(animate);
}

// 窗口大小变化时重新调整canvas大小
function handleResize() {
  if (!canvasRef.value) return;
  
  canvasRef.value.width = window.innerWidth;
  canvasRef.value.height = window.innerHeight;
}

onMounted(() => {
  initParticles();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId);
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <canvas ref="canvasRef" class="particle-canvas"></canvas>
</template>

<style scoped>
.particle-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  opacity: 0.3; /* 保持透明度低，不干扰内容 */
}
</style>

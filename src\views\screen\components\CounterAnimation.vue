<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';

const props = defineProps({
  value: {
    type: Number,
    required: true
  },
  duration: {
    type: Number,
    default: 1500
  },
  decimals: {
    type: Number,
    default: 0
  },
  prefix: {
    type: String,
    default: ''
  },
  suffix: {
    type: String,
    default: ''
  }
});

const displayValue = ref(0);
const animationStarted = ref(false);

// 数字滚动动画
function animateValue() {
  if (animationStarted.value) return;

  animationStarted.value = true;
  const startValue = 0;
  const endValue = props.value;
  const startTime = performance.now();

  function updateValue(currentTime: number) {
    const elapsedTime = currentTime - startTime;

    if (elapsedTime < props.duration) {
      const progress = elapsedTime / props.duration;
      // 使用缓动函数使动画更自然
      const easedProgress = easeOutQuart(progress);
      displayValue.value = startValue + (endValue - startValue) * easedProgress;
      requestAnimationFrame(updateValue);
    } else {
      displayValue.value = endValue;
      animationStarted.value = false;
    }
  }

  requestAnimationFrame(updateValue);
}

// 缓动函数 - 快速开始，缓慢结束
function easeOutQuart(x: number): number {
  return 1 - Math.pow(1 - x, 4);
}

// 格式化数字
function formatNumber(num: number): string {
  return props.prefix + num.toFixed(props.decimals) + props.suffix;
}

// 监听值变化
watch(() => props.value, () => {
  animateValue();
});

onMounted(() => {
  animateValue();
});
</script>

<template>
  <div class="counter-animation">
    <span class="counter-value">{{ formatNumber(Math.floor(displayValue)) }}</span>
  </div>
</template>

<style scoped>
.counter-animation {
  display: inline-block;
}

.counter-value {
  font-family: 'Digital Font', 'Consolas', 'Courier New', monospace;
  transition: text-shadow 0.3s ease;
}

.counter-value:hover {
  text-shadow: 0 0 15px rgba(0, 255, 255, 0.9);
}
</style>

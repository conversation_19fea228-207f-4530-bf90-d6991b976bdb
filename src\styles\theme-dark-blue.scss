// Dark Blue Theme for Three Gorges Dam Monitoring Platform

// Main colors
$primary-dark-blue: #0a1a3a;
$secondary-dark-blue: #0d2252;
$highlight-blue: #00a8ff;
$glow-blue: #00c6ff;
$text-light: #ffffff;
$text-highlight: #00ffff;
$border-glow: #0088ff;
$map-highlight: #00a8ff;
$data-highlight: #00ffff;
$nav-active: #00a8ff;
$nav-inactive: #1e3c72;

// Global styles
body {
  background-color: $primary-dark-blue;
  color: $text-light;
  font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
}

// Container styles
.screen-container {
  background-color: $primary-dark-blue;
  background-image: linear-gradient(to bottom, $secondary-dark-blue, $primary-dark-blue);
  color: $text-light;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('/static/img/grid-pattern.png');
    background-size: 20px 20px;
    opacity: 0.1;
    pointer-events: none;
    z-index: 1;
  }
}

// Header styles
.screen-header {
  border-bottom: 1px solid rgba($border-glow, 0.3);
  position: relative;
  height: 60px;
  
  .title {
    color: $text-light;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba($glow-blue, 0.5);
    letter-spacing: 2px;
  }
}

// Navigation tabs
.nav-tabs {
  display: flex;
  margin-top: 5px;
  border-bottom: 1px solid rgba($border-glow, 0.2);
  
  .nav-tab {
    padding: 8px 20px;
    margin-right: 2px;
    background-color: $nav-inactive;
    color: $text-light;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.active {
      background-color: $nav-active;
      color: $text-highlight;
      box-shadow: 0 0 15px rgba($glow-blue, 0.5);
    }
    
    &:hover:not(.active) {
      background-color: lighten($nav-inactive, 10%);
    }
  }
}

// Map visualization
.map-container {
  position: relative;
  
  .map-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    background: radial-gradient(circle at center, transparent 30%, rgba($primary-dark-blue, 0.8) 100%);
    z-index: 2;
  }
}

// Data display components
.data-panel {
  background-color: rgba($secondary-dark-blue, 0.7);
  border: 1px solid rgba($border-glow, 0.3);
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 0 20px rgba($glow-blue, 0.2);
  
  .panel-title {
    color: $text-highlight;
    font-size: 16px;
    margin-bottom: 10px;
    border-bottom: 1px solid rgba($border-glow, 0.3);
    padding-bottom: 5px;
  }
  
  .data-value {
    font-family: 'Digital-7', monospace;
    font-size: 28px;
    color: $data-highlight;
    text-shadow: 0 0 10px rgba($data-highlight, 0.7);
  }
  
  .data-label {
    font-size: 14px;
    color: rgba($text-light, 0.8);
  }
}

// Digital counter style
.digital-counter {
  font-family: 'Digital-7', monospace;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba($border-glow, 0.5);
  border-radius: 3px;
  padding: 5px 10px;
  color: $data-highlight;
  text-shadow: 0 0 8px rgba($data-highlight, 0.7);
  display: inline-block;
  letter-spacing: 2px;
}

// Glowing elements
.glow-border {
  border: 1px solid rgba($border-glow, 0.5);
  box-shadow: 0 0 15px rgba($glow-blue, 0.3);
  border-radius: 5px;
}

.glow-text {
  color: $text-highlight;
  text-shadow: 0 0 10px rgba($text-highlight, 0.7);
}

// Chart customization
.echarts-container {
  .echarts-title {
    color: $text-highlight;
    font-size: 14px;
  }
}

// Custom scrollbar
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba($secondary-dark-blue, 0.3);
}

::-webkit-scrollbar-thumb {
  background: rgba($highlight-blue, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba($highlight-blue, 0.7);
}

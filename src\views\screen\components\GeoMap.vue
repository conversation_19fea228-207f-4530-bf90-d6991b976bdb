<template>
  <div ref="chartDom" class="echarts-map-container"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, shallowRef } from 'vue';
// 引入 ECharts 核心模块
import * as echarts from 'echarts/core';
// 引入图表类型，MapChart 和 CustomChart 可能与 geo 组件配合使用
import { MapChart, CustomChart } from 'echarts/charts';
// 引入组件
import {
  TooltipComponent,
  GeoComponent // 关键：使用 GeoComponent 来处理自定义或复杂的地理数据
} from 'echarts/components';
// 引入渲染器
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  MapChart,      // 即使主要用 geo，有时也需注册 MapChart
  CustomChart,   // 备用，以防需要更复杂的绘制
  TooltipComponent,
  GeoComponent,  // 必须注册 GeoComponent
  CanvasRenderer
]);

// --- 配置项 ---
// 1. GeoJSON 文件路径 (相对于 public 目录)
// *** 修改这里为你 GeoJSON 文件的实际路径 ***
const geoJsonPath = '/static/map/lake.json'; // 例如: '/data/shapes.geojson'

// 2. 注册的地图/形状名称
const mapName = 'MyGeometryCollectionMap';
// --- 配置项结束 ---

const chartDom = ref(null);
const chartInstance = shallowRef(null);
let resizeObserver = null;

/**
 * 预处理 GeoJSON: 将 GeometryCollection 转换为 FeatureCollection
 * ECharts 的 geo 组件和 map series 更习惯处理 FeatureCollection，
 * 尤其是需要为每个形状添加 tooltip 或绑定数据时。
 * @param {object} geoJsonInput 原始 GeoJSON 对象
 * @returns {object} 处理后的 FeatureCollection GeoJSON 对象
 */
const processGeoJson = (geoJsonInput) => {
  if (!geoJsonInput || geoJsonInput.type !== 'GeometryCollection' || !Array.isArray(geoJsonInput.geometries)) {
    console.warn("输入的 GeoJSON 不是有效的 GeometryCollection，尝试直接使用。");
    // 如果不是 GeometryCollection 或格式无效，直接返回，让 ECharts 尝试处理
    // 但后续的 tooltip 等可能不如预期工作
    return geoJsonInput;
  }

  console.log("检测到 GeometryCollection，正在转换为 FeatureCollection...");
  const features = geoJsonInput.geometries.map((geometry, index) => {
    // 为每个 geometry 创建一个 Feature
    return {
      type: 'Feature',
      properties: {
        // 添加一个唯一的、可识别的 name 属性，用于 tooltip 或后续数据绑定
        // 你可以根据实际需要从 geometry 的其他属性（如果存在）生成更有意义的 name
        name: `形状 ${index + 1}`
      },
      geometry: geometry // 保留原始的 geometry
    };
  });

  // 返回符合 FeatureCollection 规范的对象
  return {
    type: 'FeatureCollection',
    features: features
  };
};


const initChart = async () => {
  if (!chartDom.value) {
    console.error("DOM 元素未准备好");
    return;
  }

  try {
    // 1. 获取 GeoJSON 数据
    const response = await fetch(geoJsonPath);
    if (!response.ok) {
      throw new Error(`无法加载 GeoJSON 文件: ${response.statusText} (路径: ${geoJsonPath})`);
    }
    const rawGeoJsonData = await response.json();

    // 2. 预处理 GeoJSON 数据
    const processedGeoJson = processGeoJson(rawGeoJsonData);

    // 3. 注册处理后的地图数据
    // 即使使用 geo 组件，也需要先注册 map 数据
    echarts.registerMap(mapName, processedGeoJson);

    // 4. 初始化 ECharts 实例
    chartInstance.value = echarts.init(chartDom.value);

    // 5. 配置 ECharts 选项
    const options = {
      tooltip: {
        trigger: 'item', // 在图形上触发 tooltip
        formatter: (params) => {
          // 使用我们在 processGeoJson 中添加的 properties.name
          // 如果原始 GeoJSON 的 geometry 有其他属性，可以在 processGeoJson 时放入 properties
          // 然后在这里读取 params.data.properties.xxx
          return params.name || '未知形状'; // params.name 来自 feature.properties.name
        }
      },
      // 使用 geo 组件来承载和显示地图数据
      geo: {
        show: true,       // 显示 geo 组件
        map: mapName,     // 关键：指定使用我们注册的地图数据
        roam: true,       // 开启鼠标缩放和平移
        zoom: 1,          // 初始缩放级别 (ECharts 会自动调整以适应内容)
        // 对于非标准经纬度坐标，geo 组件通常能更好地自动定位和缩放
        // center 和 aspectScale 通常 ECharts 会自动计算
        label: {          // 地图上形状的标签 (显示 '形状 1' 等)
          show: false,    // 默认不显示，因为名称可能意义不大
          // 如果 processGeoJson 能生成有意义的名字，可以设为 true
        },
        itemStyle: {      // 形状的默认样式
          areaColor: '#BBDDFF',
          borderColor: '#666',
          borderWidth: 0.5
        },
        emphasis: {       // 鼠标悬浮高亮时的样式
          focus: 'self',  // 高亮当前悬浮的形状
          label: {
            show: true, // 高亮时显示名称
            color: '#000'
          },
          itemStyle: {
            areaColor: '#FFD700', // 高亮颜色
            shadowBlur: 5,
            shadowColor: 'rgba(0, 0, 0, 0.4)'
          }
        }
      },
      // (可选) 添加 series 在 geo 组件上绘制其他数据
      // 如果只是显示 GeometryCollection 的形状，可以省略 series
      // 或者添加一个空的 map series 关联 geo，有时有助于某些交互或 visualMap
      series: [
         {
           name: '形状层',   // 系列名称 (可选)
           type: 'map',      // 类型 map
           geoIndex: 0,      // 指定此系列绘制在第一个 geo 组件上
           map: mapName,     // 再次关联地图数据 (最佳实践)
           data: []          // 这里可以放与 '形状 1' 等对应的业务数据 {name: '形状 1', value: ...}
                             // 如果有 value，可以配合 visualMap 使用 (需取消 visualMap 注释)
         }
      ]
      // (可选) 如果 series.data 中有 value，可以添加 visualMap
      // visualMap: {
      //   min: 0, max: 100, calculable: true, inRange: { color: ['#50a3ba', '#eac736', '#d94e5d'] }, textStyle: { color: '#fff' }
      // }
    };

    // 6. 应用配置项
    chartInstance.value.setOption(options);

  } catch (error) {
    console.error('初始化 ECharts 地图失败:', error);
    if (chartDom.value) {
        chartDom.value.innerText = `加载地图失败: ${error.message}`;
    }
  }
};

// 处理图表大小自适应
const resizeChart = () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
};

// 组件挂载后执行
onMounted(() => {
  initChart();
  if (chartDom.value) {
    resizeObserver = new ResizeObserver(resizeChart);
    resizeObserver.observe(chartDom.value);
  }
});

// 组件卸载前执行
onBeforeUnmount(() => {
  if (resizeObserver && chartDom.value) {
    resizeObserver.unobserve(chartDom.value);
  }
  if (chartInstance.value) {
    chartInstance.value.dispose();
    chartInstance.value = null;
  }
});

</script>

<style scoped>
.echarts-map-container {
  width: 100%;
  /* *** 设置一个合适的高度 *** */
  height: 600px;
  border: 1px solid #ccc; /* 可选 */
  background-color: #f8f8f8; /* 加个背景方便看清地图范围 */
}
</style>
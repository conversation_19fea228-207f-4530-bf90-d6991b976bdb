<script setup lang="ts">
import { ref, onMounted, getCurrentInstance, reactive } from 'vue';
import LineChart from '@/views/screen/components/LineChart.vue';
import ScanEffect from '@/views/screen/components/ScanEffect.vue';

const { proxy } = getCurrentInstance() as any;
const mapContainer = ref(null);
const flowChartContainer = ref(null);

// 标记点位置信息
const markers = reactive([
  { id: 1, name: '三峡大坝', x: 55, y: 45, status: 'normal' },
  { id: 2, name: '葛洲坝', x: 60, y: 55, status: 'normal' },
  { id: 3, name: '小溪塔', x: 40, y: 35, status: 'normal' }
]);

// 初始化流量图表
function initFlowChart() {
  if (!flowChartContainer.value) return;

  const chartInstance = proxy.$echarts.init(flowChartContainer.value, 'dark-blue');

  const option = {
    title: {
      text: '小溪塔流量监测',
      left: 'center',
      textStyle: {
        color: '#00ffff'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#0088ff'
        }
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value',
      name: '流量 (m³/s)',
      nameTextStyle: {
        color: '#00ffff'
      }
    },
    series: [
      {
        name: '实时流量',
        type: 'line',
        stack: 'Total',
        data: [3200, 4500, 5800, 6700, 7200, 8500, 9800, 12000, 8500, 7200, 5800, 4200],
        smooth: true,
        lineStyle: {
          width: 3,
          shadowColor: 'rgba(0, 198, 255, 0.5)',
          shadowBlur: 10
        },
        areaStyle: {
          opacity: 0.3,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(0, 198, 255, 0.8)'
            }, {
              offset: 1, color: 'rgba(0, 198, 255, 0)'
            }]
          }
        }
      }
    ]
  };

  chartInstance.setOption(option);

  // 响应式调整
  window.addEventListener('resize', () => {
    chartInstance.resize();
  });
}

// 显示标记点详情
function showMarkerDetails(markerId: number) {
  console.log(`显示标记点 ${markerId} 的详细信息`);
  // 这里可以添加显示详情的逻辑
}

onMounted(() => {
  initFlowChart();
});
</script>

<template>
  <div class="map-wrapper">
    <div class="map-overlay">
      <div class="map-title">三峡-葛洲坝</div>
      <!-- <div class="map-subtitle">实时数据监控平台</div> -->
    </div>

    <ScanEffect>
      <div class="main-map" ref="mapContainer">
        <div class="map-grid"></div>
        <img src="./static/img/三峡流域图.png" alt="三峡流域图" class="map-image">
        <!-- <img src="/public/static/img/三峡流域图.png" alt="三峡流域图" class="map-image"> -->

        <!-- 标记点 -->
        <div
          v-for="marker in markers"
          :key="marker.id"
          class="marker"
          :class="[`marker-${marker.id}`, { 'warning': marker.status === 'warning' }]"
          :style="{ top: `${marker.y}%`, left: `${marker.x}%` }"
          @click="showMarkerDetails(marker.id)"
        >
          <div class="pulse"></div>
          <div class="marker-label">{{ marker.name }}</div>
          <div class="marker-data">状态: {{ marker.status === 'normal' ? '正常' : '警告' }}</div>
        </div>

        <!-- 连接线 -->
        <!-- <svg class="connection-lines">
          <line x1="55%" y1="45%" x2="60%" y2="55%" class="connection-line" />
          <line x1="40%" y1="35%" x2="55%" y2="45%" class="connection-line" />
        </svg> -->
      </div>
    </ScanEffect>

    <!-- 流量图表将在JS中初始化
    <div class="flow-chart" ref="flowChartContainer">

    </div>
    -->

  </div>
</template>

<style scoped>
.map-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 5px;
}

.map-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  pointer-events: none;
}

.map-title {
  font-size: 24px;
  font-weight: bold;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.7);
  margin-bottom: 5px;
}

.map-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
}

.main-map {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 网格背景 */
.map-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, rgba(0, 168, 255, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 168, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 1;
}

.map-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(1.1) contrast(1.1) saturate(1.2);
  z-index: 2;
  position: relative;
}

.flow-chart {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 350px;
  height: 250px;
  background-color: rgba(10, 26, 58, 0.7);
  border: 1px solid rgba(0, 198, 255, 0.5);
  border-radius: 5px;
  box-shadow: 0 0 15px rgba(0, 198, 255, 0.3);
  z-index: 5;
}

/* 连接线 */
.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  pointer-events: none;
}

.connection-line {
  stroke: rgba(0, 198, 255, 0.5);
  stroke-width: 2px;
  stroke-dasharray: 5, 5;
  animation: flowLine 30s linear infinite;
}

@keyframes flowLine {
  to {
    stroke-dashoffset: -1000;
  }
}

/* 标记点样式 */
.marker {
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: rgba(0, 198, 255, 0.8);
  border-radius: 50%;
  cursor: pointer;
  z-index: 4;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.marker.warning {
  background-color: rgba(255, 183, 77, 0.8);
}

.marker.warning .pulse {
  background-color: rgba(255, 183, 77, 0.8);
  box-shadow: 0 0 8px rgba(255, 183, 77, 0.8);
}

.marker:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
}

.marker-label {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  color: #ffffff;
  font-size: 12px;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
  opacity: 0.8;
  transition: opacity 0.3s, transform 0.3s;
}

.marker-data {
  position: absolute;
  top: 25px;
  left: 50%;
  transform: translateX(-50%) scale(0);
  white-space: nowrap;
  color: #00ffff;
  font-size: 11px;
  background-color: rgba(10, 26, 58, 0.8);
  padding: 3px 6px;
  border-radius: 3px;
  border: 1px solid rgba(0, 198, 255, 0.5);
  opacity: 0;
  transition: opacity 0.3s, transform 0.3s;
  pointer-events: none;
}

.marker.warning .marker-data {
  color: #ffb74d;
}

.marker:hover .marker-label {
  opacity: 1;
  transform: translateX(-50%) translateY(-5px);
}

.marker:hover .marker-data {
  opacity: 1;
  transform: translateX(-50%) scale(1);
}

/* 脉冲效果 */
.pulse {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: rgba(0, 198, 255, 0.8);
  box-shadow: 0 0 8px rgba(0, 198, 255, 0.8);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  70% {
    transform: scale(2);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
</style>
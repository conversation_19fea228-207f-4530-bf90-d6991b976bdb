import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', () => {
  // 用户信息，包含token
  const userInfo = ref({
    username: '',
    token: '',
    token_type: '',
    refresh_token: '',
    expires_in: 0,
    isLoggedIn: false
  })

  // 设置用户信息
  const setUserInfo = (info: {
    username: string,
    access_token: string,
    token_type: string,
    refresh_token: string,
    expires_in: number
  }) => {
    userInfo.value.username = info.username
    userInfo.value.token = info.access_token
    userInfo.value.token_type = info.token_type
    userInfo.value.refresh_token = info.refresh_token
    userInfo.value.expires_in = info.expires_in
    userInfo.value.isLoggedIn = true

    // 保存到本地存储，以便页面刷新后保持登录状态
    localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
  }

  // 清除用户信息
  const clearUserInfo = () => {
    userInfo.value.username = ''
    userInfo.value.token = ''
    userInfo.value.token_type = ''
    userInfo.value.refresh_token = ''
    userInfo.value.expires_in = 0
    userInfo.value.isLoggedIn = false

    // 清除本地存储
    localStorage.removeItem('userInfo')
  }

  // 从本地存储加载用户信息
  const loadUserInfo = () => {
    const storedInfo = localStorage.getItem('userInfo')
    if (storedInfo) {
      try {
        const parsedInfo = JSON.parse(storedInfo)
        userInfo.value = parsedInfo
      } catch (e) {
        console.error('Failed to parse stored user info', e)
        clearUserInfo()
      }
    }
  }

  return { userInfo, setUserInfo, clearUserInfo, loadUserInfo }
})

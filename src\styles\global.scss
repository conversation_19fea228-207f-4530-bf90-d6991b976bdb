html, body, #app {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  overflow: hidden;
}
.com-page {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.com-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.com-chart {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
//圆角
canvas{
  border-radius: 20px;
}

.com-container{
  position:relative;
}
.common-layout{
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.layout-container-demo{
  width: 100%;
  height: 100%;
  overflow: hidden;
}

// 边缘渐变（用在rainfall图）
.gradient-border {
  --border-width: 3px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 720px;
  height: 365px;
  font-family: Lato, sans-serif;
  font-size: 2.5rem;
  text-transform: uppercase;
  color: white;
  background: #222;
  border-radius: var(--border-width);

  &::after {
    position: absolute;
    content: "";
    top: calc(-1 * var(--border-width));
    left: calc(-1 * var(--border-width));
    z-index: -1;
    width: calc(100% + var(--border-width) * 2);
    height: calc(100% + var(--border-width) * 2);
    background: linear-gradient(
                    60deg,
                    hsl(224, 85%, 66%),
                    hsl(269, 85%, 66%),
                    hsl(314, 85%, 66%),
                    hsl(359, 85%, 66%),
                    hsl(44, 85%, 66%),
                    hsl(89, 85%, 66%),
                    hsl(134, 85%, 66%),
                    hsl(179, 85%, 66%)
    );
    background-size: 300% 300%;
    background-position: 0 50%;
    border-radius: calc(2 * var(--border-width));
    animation: moveGradient 4s alternate infinite;
  }
}

@keyframes moveGradient {
  50% {
    background-position: 100% 50%;
  }
}

//下载按钮CSS
.download {
  cursor: pointer;
  position: relative;
  padding: 10px 24px;
  font-size: 18px;
  color: rgb(193, 163, 98);
  border: 2px solid rgb(193, 163, 98);
  border-radius: 34px;
  background-color: transparent;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
  overflow: hidden;
}

.download::before {
  content: '';
  position: absolute;
  inset: 0;
  margin: auto;
  width: 50px;
  height: 50px;
  border-radius: inherit;
  scale: 0;
  z-index: -1;
  background-color: rgb(193, 163, 98);
  transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
}

.download:hover::before {
  scale: 3;
}

.download:hover {
  color: #212121;
  //color: white;
  scale: 1.1;
  box-shadow: 0 0px 20px rgba(193, 163, 98,0.4);
}
.download:active {
  scale: 1;
}

// 退出登录确认弹窗样式
.logout-confirm-dialog {
  border-radius: 10px;
  overflow: hidden;

  .el-message-box__header {
    background-color: #0d2252;
    color: #ffffff;
    padding: 15px 20px;

    .el-message-box__title {
      color: #00ffff;
      font-weight: bold;
    }

    .el-message-box__headerbtn .el-message-box__close {
      color: #ffffff;

      &:hover {
        color: #00a8ff;
      }
    }
  }

  .el-message-box__content {
    background-color: #0a1a3a;
    color: #ffffff;
    padding: 20px;
  }

  .el-message-box__btns {
    background-color: #0a1a3a;
    padding: 10px 20px 20px;

    .el-button {
      border-radius: 20px;
      padding: 8px 20px;

      &.el-button--primary {
        background-color: #00a8ff;
        border-color: #00a8ff;

        &:hover {
          background-color: lighten(#00a8ff, 10%);
          border-color: lighten(#00a8ff, 10%);
          box-shadow: 0 0 15px rgba(#00c6ff, 0.5);
        }
      }

      &.el-button--default {
        background-color: transparent;
        border-color: rgba(#0088ff, 0.5);
        color: #ffffff;

        &:hover {
          background-color: rgba(#00a8ff, 0.2);
          border-color: #00a8ff;
        }
      }
    }
  }
}

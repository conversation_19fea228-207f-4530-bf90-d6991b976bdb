import { ref, computed, reactive } from 'vue'
import { defineStore } from 'pinia'

export const useThemeStore = defineStore('theme', () => {
    let theme: any = reactive({
        theme: 'chalk'
    })

    function changeTheme() {
        if (theme.theme === 'chalk') {
            theme.theme = 'vintage'
            // theme = Object.assign(theme,{theme:'vintage'})
        } else {
            theme.theme = 'chalk'
        }
    }

    return { theme, changeTheme }
})

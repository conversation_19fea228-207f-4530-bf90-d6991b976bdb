<script setup lang="ts">
import { getCurrentInstance, ref, onMounted, onUnmounted, computed } from "vue";
import CommonLayout from '@/components/CommonLayout.vue';

const { proxy } = getCurrentInstance() as any;

// 图表实例
const stageChartRef = ref();
const rainFlowChartRef = ref();
let stageChartInstance: any = null;
let rainFlowChartInstance: any = null;

// 表格数据
const tableData = ref<any[]>([]);

// 时间格式化函数
function formatDateTime(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}`;
}

// 初始化时间
function initializeDateTime() {
  const now = new Date();
  const startTime = new Date(now);
  startTime.setMinutes(0, 0, 0); // 设置为当前时间的整点

  const endTime = new Date(startTime);
  endTime.setHours(endTime.getHours() + 24); // 结束时间为开始时间后24小时

  return {
    start: formatDateTime(startTime),
    end: formatDateTime(endTime)
  };
};

const { start: initialStartDate, end: initialEndDate } = initializeDateTime();
const startDate = ref(initialStartDate);
const endDate = ref(initialEndDate);
const isLoading = ref(false);

// 构建请求数据
function buildRequestData() {
  return {
    "basicParams": {
      "EndTime": endDate.value + ":59",
      "OutTimeStep": 30.0,
      "StartTime": startDate.value + ":00"
    }
    // "runDataSet": {
    //     "discharge": [
    //         [
    //             "三峡出库",
    //             "2023-06-11 00:00:00",
    //             8377.752
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-11 02:00:00",
    //             7794.5916
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-11 04:00:00",
    //             7756.8116
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-11 06:00:00",
    //             7733.4754
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-11 08:00:00",
    //             11669.1372
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-11 10:00:00",
    //             12423.4479
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-11 12:00:00",
    //             10179.2156
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-11 14:00:00",
    //             8612.549
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-11 16:00:00",
    //             8606.0318
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-11 18:00:00",
    //             12465.3787
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-11 20:00:00",
    //             12551.451
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-11 22:00:00",
    //             8732.7756
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-12 00:00:00",
    //             7895.5395
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-12 02:00:00",
    //             7870.7483
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-12 04:00:00",
    //             7857.7407
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-12 06:00:00",
    //             7844.4678
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-12 08:00:00",
    //             11878.9526
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-12 10:00:00",
    //             12637.4304
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-12 12:00:00",
    //             10332.1963
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-12 14:00:00",
    //             8766.0369
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-12 16:00:00",
    //             8709.2508
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-12 18:00:00",
    //             12637.6661
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-12 20:00:00",
    //             12674.5744
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-12 22:00:00",
    //             8744.4954
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-13 00:00:00",
    //             7912.2254
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-13 02:00:00",
    //             7899.9018
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-13 04:00:00",
    //             7881.1817
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-13 06:00:00",
    //             7868.964
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-13 08:00:00",
    //             11723.2176
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-13 10:00:00",
    //             12641.8645
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-13 12:00:00",
    //             10431.2783
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-13 14:00:00",
    //             8684.8839
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-13 16:00:00",
    //             8653.926
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-13 18:00:00",
    //             12569.4086
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-13 20:00:00",
    //             12626.1027
    //         ],
    //         [
    //             "三峡出库",
    //             "2023-06-13 22:00:00",
    //             8712.5308
    //         ]
    //     ],
    //     "rain": [
    //         [
    //             "雾渡河",
    //             "2023-06-11 18:59:00",
    //             "2023-06-11 20:00:00",
    //             2.5
    //         ],
    //         [
    //             "雾渡河",
    //             "2023-06-11 19:59:00",
    //             "2023-06-11 21:00:00",
    //             60.0
    //         ],
    //         [
    //             "雾渡河",
    //             "2023-06-11 20:59:00",
    //             "2023-06-11 22:00:00",
    //             2.0
    //         ],
    //         [
    //             "雾渡河",
    //             "2023-06-11 21:59:00",
    //             "2023-06-11 23:00:00",
    //             5.5
    //         ],
    //         [
    //             "雾渡河",
    //             "2023-06-11 22:59:00",
    //             "2023-06-12 00:00:00",
    //             2.0
    //         ],
    //         [
    //             "雾渡河",
    //             "2023-06-12 20:59:00",
    //             "2023-06-12 22:00:00",
    //             23.0
    //         ],
    //         [
    //             "雾渡河",
    //             "2023-06-12 21:59:00",
    //             "2023-06-12 23:00:00",
    //             3.5
    //         ],
    //         [
    //             "雾渡河",
    //             "2023-06-12 22:59:00",
    //             "2023-06-13 00:00:00",
    //             1.5
    //         ],
    //         [
    //             "雾渡河",
    //             "2023-06-12 23:59:00",
    //             "2023-06-13 01:00:00",
    //             0.5
    //         ],
    //         [
    //             "分乡",
    //             "2023-06-11 18:59:00",
    //             "2023-06-11 20:00:00",
    //             2.5
    //         ],
    //         [
    //             "分乡",
    //             "2023-06-11 19:59:00",
    //             "2023-06-11 21:00:00",
    //             60.0
    //         ],
    //         [
    //             "分乡",
    //             "2023-06-11 20:59:00",
    //             "2023-06-11 22:00:00",
    //             2.0
    //         ],
    //         [
    //             "分乡",
    //             "2023-06-11 21:59:00",
    //             "2023-06-11 23:00:00",
    //             5.5
    //         ],
    //         [
    //             "分乡",
    //             "2023-06-11 22:59:00",
    //             "2023-06-12 00:00:00",
    //             2.0
    //         ],
    //         [
    //             "分乡",
    //             "2023-06-12 20:59:00",
    //             "2023-06-12 22:00:00",
    //             23.0
    //         ],
    //         [
    //             "分乡",
    //             "2023-06-12 21:59:00",
    //             "2023-06-12 23:00:00",
    //             3.5
    //         ],
    //         [
    //             "分乡",
    //             "2023-06-12 22:59:00",
    //             "2023-06-13 00:00:00",
    //             1.5
    //         ],
    //         [
    //             "分乡",
    //             "2023-06-12 23:59:00",
    //             "2023-06-13 01:00:00",
    //             0.5
    //         ],
    //         [
    //             "小溪塔",
    //             "2023-06-11 18:59:00",
    //             "2023-06-11 20:00:00",
    //             2.5
    //         ],
    //         [
    //             "小溪塔",
    //             "2023-06-11 19:59:00",
    //             "2023-06-11 21:00:00",
    //             60.0
    //         ],
    //         [
    //             "小溪塔",
    //             "2023-06-11 20:59:00",
    //             "2023-06-11 22:00:00",
    //             2.0
    //         ],
    //         [
    //             "小溪塔",
    //             "2023-06-11 21:59:00",
    //             "2023-06-11 23:00:00",
    //             5.5
    //         ],
    //         [
    //             "小溪塔",
    //             "2023-06-11 22:59:00",
    //             "2023-06-12 00:00:00",
    //             2.0
    //         ],
    //         [
    //             "小溪塔",
    //             "2023-06-12 20:59:00",
    //             "2023-06-12 22:00:00",
    //             23.0
    //         ],
    //         [
    //             "小溪塔",
    //             "2023-06-12 21:59:00",
    //             "2023-06-12 23:00:00",
    //             3.5
    //         ],
    //         [
    //             "小溪塔",
    //             "2023-06-12 22:59:00",
    //             "2023-06-13 00:00:00",
    //             1.5
    //         ],
    //         [
    //             "小溪塔",
    //             "2023-06-12 23:59:00",
    //             "2023-06-13 01:00:00",
    //             0.5
    //         ],
    //         [
    //             "泗溪",
    //             "2023-06-11 18:59:00",
    //             "2023-06-11 20:00:00",
    //             2.5
    //         ],
    //         [
    //             "泗溪",
    //             "2023-06-11 19:59:00",
    //             "2023-06-11 21:00:00",
    //             60.0
    //         ],
    //         [
    //             "泗溪",
    //             "2023-06-11 20:59:00",
    //             "2023-06-11 22:00:00",
    //             2.0
    //         ],
    //         [
    //             "泗溪",
    //             "2023-06-11 21:59:00",
    //             "2023-06-11 23:00:00",
    //             5.5
    //         ],
    //         [
    //             "泗溪",
    //             "2023-06-11 22:59:00",
    //             "2023-06-12 00:00:00",
    //             2.0
    //         ],
    //         [
    //             "泗溪",
    //             "2023-06-12 20:59:00",
    //             "2023-06-12 22:00:00",
    //             23.0
    //         ],
    //         [
    //             "泗溪",
    //             "2023-06-12 21:59:00",
    //             "2023-06-12 23:00:00",
    //             3.5
    //         ],
    //         [
    //             "泗溪",
    //             "2023-06-12 22:59:00",
    //             "2023-06-13 00:00:00",
    //             1.5
    //         ],
    //         [
    //             "泗溪",
    //             "2023-06-12 23:59:00",
    //             "2023-06-13 01:00:00",
    //             0.5
    //         ],
    //         [
    //             "沙坪",
    //             "2023-06-11 18:59:00",
    //             "2023-06-11 20:00:00",
    //             2.5
    //         ],
    //         [
    //             "沙坪",
    //             "2023-06-11 19:59:00",
    //             "2023-06-11 21:00:00",
    //             60.0
    //         ],
    //         [
    //             "沙坪",
    //             "2023-06-11 20:59:00",
    //             "2023-06-11 22:00:00",
    //             2.0
    //         ],
    //         [
    //             "沙坪",
    //             "2023-06-11 21:59:00",
    //             "2023-06-11 23:00:00",
    //             5.5
    //         ],
    //         [
    //             "沙坪",
    //             "2023-06-11 22:59:00",
    //             "2023-06-12 00:00:00",
    //             2.0
    //         ],
    //         [
    //             "沙坪",
    //             "2023-06-12 20:59:00",
    //             "2023-06-12 22:00:00",
    //             23.0
    //         ],
    //         [
    //             "沙坪",
    //             "2023-06-12 21:59:00",
    //             "2023-06-12 23:00:00",
    //             3.5
    //         ],
    //         [
    //             "沙坪",
    //             "2023-06-12 22:59:00",
    //             "2023-06-13 00:00:00",
    //             1.5
    //         ],
    //         [
    //             "沙坪",
    //             "2023-06-12 23:59:00",
    //             "2023-06-13 01:00:00",
    //             0.5
    //         ]
    //     ],
    //     "stage": [
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 00:00:00",
    //             64.572
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 01:00:00",
    //             64.435
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 02:00:00",
    //             64.226
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 03:00:00",
    //             64.184
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 04:00:00",
    //             64.117
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 05:00:00",
    //             63.977
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 06:00:00",
    //             63.926
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 07:00:00",
    //             63.808
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 08:00:00",
    //             63.846
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 09:00:00",
    //             64.129
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 10:00:00",
    //             64.197
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 11:00:00",
    //             64.414
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 12:00:00",
    //             64.625
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 13:00:00",
    //             64.529
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 14:00:00",
    //             64.488
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 15:00:00",
    //             64.516
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 16:00:00",
    //             64.41
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 17:00:00",
    //             64.487
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 18:00:00",
    //             64.655
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 19:00:00",
    //             64.749
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 20:00:00",
    //             64.959
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 21:00:00",
    //             65.044
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 22:00:00",
    //             65.193
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-11 23:00:00",
    //             64.966
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 00:00:00",
    //             64.87
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 01:00:00",
    //             64.813
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 02:00:00",
    //             64.693
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 03:00:00",
    //             64.74
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 04:00:00",
    //             64.701
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 05:00:00",
    //             64.672
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 06:00:00",
    //             64.639
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 07:00:00",
    //             64.476
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 08:00:00",
    //             64.622
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 09:00:00",
    //             64.907
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 10:00:00",
    //             64.996
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 11:00:00",
    //             65.233
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 12:00:00",
    //             65.439
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 13:00:00",
    //             65.257
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 14:00:00",
    //             65.178
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 15:00:00",
    //             65.065
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 16:00:00",
    //             64.807
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 17:00:00",
    //             64.802
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 18:00:00",
    //             65.046
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 19:00:00",
    //             65.11
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 20:00:00",
    //             65.176
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 21:00:00",
    //             65.243
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 22:00:00",
    //             65.193
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-12 23:00:00",
    //             64.945
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 00:00:00",
    //             64.861
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 01:00:00",
    //             64.736
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 02:00:00",
    //             64.799
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 03:00:00",
    //             64.692
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 04:00:00",
    //             64.751
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 05:00:00",
    //             64.674
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 06:00:00",
    //             64.616
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 07:00:00",
    //             64.638
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 08:00:00",
    //             64.674
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 09:00:00",
    //             64.849
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 10:00:00",
    //             65.062
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 11:00:00",
    //             65.148
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 12:00:00",
    //             65.24
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 13:00:00",
    //             65.072
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 14:00:00",
    //             64.835
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 15:00:00",
    //             64.683
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 16:00:00",
    //             64.627
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 17:00:00",
    //             64.561
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 18:00:00",
    //             64.836
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 19:00:00",
    //             64.879
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 20:00:00",
    //             65.081
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 21:00:00",
    //             65.222
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 22:00:00",
    //             65.304
    //         ],
    //         [
    //             "葛洲坝上游",
    //             "2023-06-13 23:00:00",
    //             65.098
    //         ]
    //     ]
    // }
  };
}

// 初始化图表
function initCharts() {
  stageChartInstance = proxy.$echarts.init(stageChartRef.value, 'dark-blue');
  rainFlowChartInstance = proxy.$echarts.init(rainFlowChartRef.value, 'dark-blue');

  const initOption = {};
  stageChartInstance.setOption(initOption);
  rainFlowChartInstance.setOption(initOption);
}

// 更新水位图表
function updateStageChart(stageData: any[]) {
  if (!stageData || stageData.length === 0) return;

  // 按站点分组数据
  const stationData: { [key: string]: any[] } = {};
  stageData.forEach(([station, time, value]: [string, string, number]) => {
    if (!stationData[station]) {
      stationData[station] = [];
    }
    stationData[station].push([time, value]);
  });

  // 定义不同的颜色数组，确保颜色差异明显且符合页面风格
  const stationColors = [
    '#ff6464', // 红色
    '#4b9afa', // 蓝色
    '#00ff88', // 绿色
    '#ffaa00', // 橙色
    '#ff44aa', // 粉色
    '#44ffff', // 青色
    '#aa44ff', // 紫色
    '#88ff44'  // 黄绿色
  ];

  const series = Object.keys(stationData).map((station, index) => ({
    name: station,
    type: 'line',
    data: stationData[station],
    smooth: true,
    lineStyle: {
      width: 3,
      color: stationColors[index % stationColors.length]
    },
    symbol: 'circle',
    symbolSize: 6,
    itemStyle: {
      color: stationColors[index % stationColors.length]
    }
  }));

  const option = {
    title: {
      text: '水位过程图',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#00ffff'
      },
      top: '10px',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      formatter: function(params: any[]) {
        let result = params[0].value[0] + '<br/>';
        // let result = 'null';
        params.forEach((param: any) => {
          // 出库流量保留整数，水位保留两位小数
          let value;
          if (param.seriesName === '三峡坝下') {
            value = param.value[1].toFixed(2);
          } else {
            value = param.value[1].toFixed(2);
          }
          result += param.marker + ' ' + param.seriesName + ': ' + value +
                    (param.seriesName === '区间入流' ? ' m³/s' : ' m') + '<br/>';
        });
        return result;
      }
    },
    legend: {
      data: Object.keys(stationData),
      textStyle: { color: '#ffffff', fontSize: 12 },
      top: '40px'
    },
    grid: {
      left: '60px',
      right: '30px',
      top: '80px',
      bottom: '60px'
    },
    xAxis: {
      type: 'time',
      axisLabel: {
        color: '#ffffff',
        fontSize: 12,
        rotate: 0,
        formatter: function(value: any) {
          const date = new Date(value);
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hour = String(date.getHours()).padStart(2, '0');
          const minute = String(date.getMinutes()).padStart(2, '0');
          return `${month}-${day} ${hour}:${minute}`;
        }
      },
      axisLine: { lineStyle: { color: '#0088ff' } },
      splitNumber: 5 // 减少横坐标刻度数量
    },
    yAxis: {
      type: 'value',
      name: '水位(m)',
      nameTextStyle: { color: '#00ffff' },
      axisLabel: {
        color: '#ffffff',
        fontSize: 12,
        // formatter: '{value}'
        formatter: function(value: number) {
            // 出库流量轴保留整数
            return value.toFixed(2);
          },
      },
      axisLine: { lineStyle: { color: '#0088ff' } },
      splitLine: { lineStyle: { color: 'rgba(0, 136, 255, 0.2)' } },
      scale: true,
    },
    series: series
  };

  stageChartInstance.setOption(option);
}

// 更新降雨流量组合图表
function updateRainFlowChart(rainData: any[], watershedData: any[]) {
  if ((!rainData || rainData.length === 0) && (!watershedData || watershedData.length === 0)) return;

  // 处理降雨数据
  const rainTimes = rainData ? rainData.map(([time]: [string, number]) => time) : [];
  const rainValues = rainData ? rainData.map(([, value]: [string, number]) => -value) : []; // 负值实现倒立效果

  // 处理流量数据
  const flowTimes = watershedData ? watershedData.map(([time]: [string, number]) => time) : [];
  const flowValues = watershedData ? watershedData.map(([, value]: [string, number]) => value) : [];

  const option = {
    title: {
      text: '降雨径流过程',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#00ffff'
      },
      top: '10px',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: function(params: any[]) {
        let result = params[0].value[0] + '<br/>';
        // let result = 'null';
        params.forEach((param: any) => {
          // 出库流量保留整数，水位保留两位小数
          let value;
          if (param.seriesName === '区间入流') {
            value = Math.round(param.value[1]).toString();
          } else {
            value = -param.value[1].toFixed(2);
          }
          result += param.marker + ' ' + param.seriesName + ': ' + value +
                    (param.seriesName === '区间入流' ? ' m³/s' : ' m') + '<br/>';
        });
        return result;
      }
    },
    legend: {
      data: ['降雨量', '区间入流'],
      textStyle: { color: '#ffffff', fontSize: 12 },
      top: '40px'
    },
    grid: {
      left: '60px',
      right: '80px',
      top: '80px',
      bottom: '60px'
    },
    xAxis: {
      type: 'time',
      axisLabel: {
        color: '#ffffff',
        fontSize: 12,
        rotate: 0,
        formatter: function(value: any) {
          const date = new Date(value);
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hour = String(date.getHours()).padStart(2, '0');
          const minute = String(date.getMinutes()).padStart(2, '0');
          return `${month}-${day} ${hour}:${minute}`;
        }
      },
      axisLine: { lineStyle: { color: '#0088ff' } },
      splitNumber: 5 // 减少横坐标刻度数量
    },
    yAxis: [
      {
        type: 'value',
        name: '流量(m³/s)',
        nameTextStyle: { color: '#00ffff' },
        position: 'left',
        axisLabel: {
          color: '#ffffff',
          fontSize: 12
        },
        axisLine: { lineStyle: { color: '#0088ff' } },
        // axisLine: { lineStyle: { color: '#00ff88' } },
        splitLine: { lineStyle: { color: 'rgba(0, 136, 255, 0.2)' } },
        // max: 2*Math.max(...flowValues)
      },
      {
        type: 'value',
        name: '降雨量(mm)',
        nameTextStyle: { color: '#00ffff' },
        position: 'right',
        axisLabel: {
          color: '#ffffff',
          fontSize: 12,
          formatter: function(value: number) {
            return Math.abs(value).toFixed(2); // 显示正值
            
          }
        },
        axisLine: { lineStyle: { color: '#ff6464' } },
        // axisLine: { lineStyle: { color: '#4b9afa' } },
        splitLine: { show: false },
        // 取rainTimes的最大值作为y轴最大值
        // min: -2*Math.min(...rainValues)
      }
    ],
    series: [
      {
        name: '区间入流',
        type: 'line',
        yAxisIndex: 0,
        data: flowTimes.map((time, index) => [time, flowValues[index]]),
        smooth: true,
        lineStyle: { width: 3, color: '#00ff88' },
        symbol: 'circle',
        symbolSize: 6,
        areaStyle: {
          opacity: 0.2,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(0, 255, 136, 0.5)'
            }, {
              offset: 1, color: 'rgba(0, 255, 136, 0)'
            }]
          }
        }
      },
      {
        name: '降雨量',
        type: 'bar',
        yAxisIndex: 1,
        data: rainTimes.map((time, index) => [time, rainValues[index]]),
        itemStyle: {
          color: '#4b9afa'
        },
        barWidth: '60%'
      }
    ]
  };

  rainFlowChartInstance.setOption(option);
}



// 处理响应数据
function processResponseData(data: any) {
  const { rain, stage, volume, watershed } = data;

  // 更新图表
  if (stage) updateStageChart(stage);
  if (rain && watershed) updateRainFlowChart(rain, watershed);

  // 处理表格数据
  if (stage && volume && watershed) {
    const processedTableData: any[] = [];

    // 获取所有时间点
    const timeSet = new Set<string>();
    stage.forEach(([, time]: [string, string, number]) => timeSet.add(time));
    volume.forEach(([, time]: [string, string, number]) => timeSet.add(time));
    watershed.forEach(([time]: [string, number]) => timeSet.add(time));

    const sortedTimes = Array.from(timeSet).sort();

    sortedTimes.forEach(time => {
      const row: any = { time };

      // 添加各站点水位数据
      const stageAtTime = stage.filter(([, t]: [string, string, number]) => t === time);
      stageAtTime.forEach(([station, , value]: [string, string, number]) => {
        row[station] = value.toFixed(2);
      });

      // 添加库容数据
      const volumeAtTime = volume.find(([, t]: [string, string, number]) => t === time);
      if (volumeAtTime) {
        row['总库容'] = volumeAtTime[2].toFixed(2);
      }

      // 添加区间入流数据
      const watershedAtTime = watershed.find(([t]: [string, number]) => t === time);
      if (watershedAtTime) {
        row['区间入流'] = Math.round(watershedAtTime[1]).toString();
      }

      processedTableData.push(row);
    });

    tableData.value = processedTableData;
  }
}

// 执行计算
async function runCalculation() {
  if (isLoading.value) return;

  try {
    isLoading.value = true;

    const requestData = buildRequestData();
    console.log('发送请求数据:', requestData);

    const response = await fetch('/api/api/run_project/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('接收到的响应数据:', result);

    processResponseData(result);
  } catch (error) {
    console.error('API请求失败:', error);
    // 使用模拟数据
    const mockData = {
      "rain": [
        ["2023-06-11 00:00:00", 0.0],
        ["2023-06-11 01:00:00", 0.0],
        ["2023-06-11 02:00:00", 0.0],
        ["2023-06-11 20:00:00", 2.5],
        ["2023-06-11 21:00:00", 1.2],
        ["2023-06-11 22:00:00", 0.8],
        ["2023-06-12 00:00:00", 0.5],
        ["2023-06-12 01:00:00", 0.2],
        ["2023-06-13 22:00:00", 0.0]
      ],
      "stage": [
        ["三峡坝下", "2023-06-11 00:00:00", 65.0],
        ["坝河口", "2023-06-11 00:00:00", 65.0],
        ["乐天溪", "2023-06-11 00:00:00", 65.0],
        ["莲沱", "2023-06-11 00:00:00", 65.0],
        ["狮子垴", "2023-06-11 00:00:00", 65.0],
        ["平善坝", "2023-06-11 00:00:00", 65.0],
        ["南津关", "2023-06-11 00:00:00", 65.0],
        ["葛洲坝", "2023-06-11 00:00:00", 65.0],
        ["三峡坝下", "2023-06-13 22:00:00", 65.30],
        ["坝河口", "2023-06-13 22:00:00", 65.30],
        ["乐天溪", "2023-06-13 22:00:00", 65.30],
        ["莲沱", "2023-06-13 22:00:00", 65.30],
        ["狮子垴", "2023-06-13 22:00:00", 65.30],
        ["平善坝", "2023-06-13 22:00:00", 65.30],
        ["南津关", "2023-06-13 22:00:00", 65.30],
        ["葛洲坝", "2023-06-13 22:00:00", 65.30]
      ],
      "volume": [
        ["总库容", "2023-06-11 00:00:00", 0.0],
        ["总库容", "2023-06-11 01:30:00", 6.57],
        ["总库容", "2023-06-11 03:00:00", 6.46],
        ["总库容", "2023-06-11 06:00:00", 6.26],
        ["总库容", "2023-06-11 12:00:00", 6.49],
        ["总库容", "2023-06-11 18:00:00", 6.46],
        ["总库容", "2023-06-13 22:00:00", 238.01]
      ],
      "watershed": [
        ["2023-06-11 00:00:00", 0.0],
        ["2023-06-11 20:00:00", 0.0006],
        ["2023-06-11 21:00:00", 0.101],
        ["2023-06-11 22:00:00", 1.012],
        ["2023-06-11 23:00:00", 3.788],
        ["2023-06-12 00:00:00", 7.436],
        ["2023-06-12 06:00:00", 30.565],
        ["2023-06-12 12:00:00", 40.931],
        ["2023-06-13 22:00:00", 238.011]
      ]
    };
    processResponseData(mockData);
  } finally {
    isLoading.value = false;
  }
}

// 屏幕适配
function screenAdapter() {
  stageChartInstance?.resize();
  rainFlowChartInstance?.resize();
}

onMounted(() => {
  initCharts();
  window.addEventListener('resize', screenAdapter);
});

onUnmounted(() => {
  window.removeEventListener('resize', screenAdapter);
  stageChartInstance?.dispose();
  rainFlowChartInstance?.dispose();
});
</script>

<template>
  <CommonLayout>
    <div class="container">
      <div class="page-title">水文水动力模型</div>
      <div class="content-container">
        <!-- 顶部控制区域 -->
        <div class="control-panel">
          <div class="time-section">
            <span class="time-label">起止时间：</span>
            <div class="time-inputs">
              <el-date-picker
                v-model="startDate"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                class="date-picker"
              />
              <span class="time-separator">至</span>
              <el-date-picker
                v-model="endDate"
                type="datetime"
                placeholder="选择结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                class="date-picker"
              />
            </div>
          </div>
          <div class="action-section">
            <el-button
              type="primary"
              @click="runCalculation"
              :loading="isLoading"
              class="calculate-btn"
            >
              {{ isLoading ? '计算中...' : '计算' }}
            </el-button>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-container">
          <!-- 左侧图表 -->
          <div class="left-charts">
            <div class="chart-wrapper">
              <div ref="stageChartRef" class="chart"></div>
            </div>
          </div>

          <!-- 右侧图表 -->
          <div class="right-charts">
            <div class="chart-wrapper">
              <div ref="rainFlowChartRef" class="chart"></div>
            </div>
          </div>
        </div>

        <!-- 底部表格区域 -->
        <div class="table-section">
          <div class="table-wrapper">
            <el-table
              :data="tableData"
              class="custom-table"
              :header-cell-style="{backgroundColor: 'rgba(13, 34, 82, 0.8)', color: '#00ffff', fontWeight: 'bold', fontSize: '14px'}"
              :row-style="{backgroundColor: 'rgba(10, 26, 58, 0.6)'}"
              :cell-style="{color: '#ffffff', fontSize: '13px'}"
              table-layout="fixed"
              size="small"
              height="100%"
              style="width: 100%;"
            >
              <el-table-column prop="time" label="计算时间" min-width="140" align="center" />
              <el-table-column prop="三峡坝下" label="三峡(m)" min-width="80" align="center" />
              <el-table-column prop="坝河口" label="现河口(m)" min-width="90" align="center" />
              <el-table-column prop="乐天溪" label="乐天溪(m)" min-width="90" align="center" />
              <el-table-column prop="莲沱" label="莲沱(m)" min-width="80" align="center" />
              <el-table-column prop="狮子垴" label="狮子垴(m)" min-width="90" align="center" />
              <el-table-column prop="平善坝" label="平善坝(m)" min-width="90" align="center" />
              <el-table-column prop="南津关" label="南津关(m)" min-width="90" align="center" />
              <el-table-column prop="葛洲坝" label="葛洲坝(m)" min-width="90" align="center" />
              <el-table-column prop="总库容" label="总库容(亿m³)" min-width="110" align="center" />
              <el-table-column prop="区间入流" label="区间入流(m³/s)" min-width="120" align="center" />
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </CommonLayout>
</template>

<style lang="scss" scoped>
// 主题颜色变量
$primary-dark-blue: #0a1a3a;
$secondary-dark-blue: #0d2252;
$highlight-blue: #00a8ff;
$glow-blue: #00c6ff;
$text-light: #ffffff;
$text-highlight: #00ffff;
$border-glow: #0088ff;
$data-highlight: #00ffff;

/* 修复表格右侧白色区域 */
:deep(.el-table) {
  background-color: transparent;
  --el-table-tr-bg-color: transparent;
  --el-table-bg-color: transparent;
  --el-table-border-color: rgba(0, 136, 255, 0.3);
}

:deep(.el-table__inner-wrapper::before) {
  display: none;
}

:deep(.el-table__fixed-right) {
  background-color: rgba(10, 26, 58, 0.6);
  height: 100% !important;
}

:deep(.el-table__fixed-right-patch) {
  background-color: rgba(13, 34, 82, 0.8);
}

:deep(.el-table__cell) {
  background-color: transparent;
}

:deep(.el-table--border::after),
:deep(.el-table--border::before),
:deep(.el-table__inner-wrapper::before) {
  background-color: rgba(0, 136, 255, 0.3);
}

:deep(.el-table__fixed::before),
:deep(.el-table__fixed-right::before) {
  background-color: transparent;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: calc(100vh - 120px);
  color: $text-light;
  overflow-y: auto;
  padding: 0px;
  box-sizing: border-box;
}

.page-title {
  font-size: 26px;
  font-weight: bold;
  color: $text-highlight;
  text-shadow: 0 0 10px rgba($glow-blue, 0.7);
  margin-bottom: 20px;
  text-align: center;
  letter-spacing: 2px;
}

.content-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 0 20px;
  box-sizing: border-box;
}

.control-panel {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  padding: 15px;
  background-color: rgba($secondary-dark-blue, 0.7);
  border: 1px solid rgba($border-glow, 0.3);
  border-radius: 8px;
  box-shadow: 0 0 20px rgba($glow-blue, 0.2);
}

.time-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.time-label {
  color: $text-highlight;
  font-size: 16px;
  font-weight: bold;
  white-space: nowrap;
}

.time-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.time-separator {
  color: $text-light;
  font-size: 16px;
  margin: 0 5px;
}

.date-picker {
  width: 180px;
  --el-input-bg-color: rgba(10, 26, 58, 0.6);
  --el-input-text-color: #ffffff;
  --el-input-border-color: rgba(0, 136, 255, 0.5);
  --el-input-hover-border-color: rgba(0, 168, 255, 0.8);
}

:deep(.el-input__wrapper) {
  background-color: rgba(10, 26, 58, 0.6);
  box-shadow: 0 0 0 1px rgba(0, 136, 255, 0.5) inset;
}

.calculate-btn {
  --el-button-bg-color: rgba(0, 168, 255, 0.8);
  --el-button-border-color: rgba(0, 168, 255, 0.8);
  --el-button-hover-bg-color: rgba(0, 168, 255, 1);
  --el-button-hover-border-color: rgba(0, 168, 255, 1);
  --el-button-text-color: #ffffff;
  font-size: 16px;
  font-weight: bold;
  padding: 12px 30px;
  border-radius: 6px;
  box-shadow: 0 0 15px rgba($highlight-blue, 0.3);

  &:hover {
    box-shadow: 0 0 25px rgba($highlight-blue, 0.5);
  }
}

.charts-container {
  display: flex;
  gap: 15px;
  height: 50%;
}

.left-charts {
  flex: 1;
}

.right-charts {
  flex: 1;
}

.chart-wrapper {
  background-color: rgba($secondary-dark-blue, 0.5);
  border: 1px solid rgba($border-glow, 0.3);
  border-radius: 8px;
  box-shadow: 0 0 15px rgba($glow-blue, 0.2);
  overflow: hidden;
  height: 100%;
}

.chart {
  width: 100%;
  height: 100%;
}

.table-section {
  height: 45%;
  display: flex;
  flex-direction: column;
}

.table-wrapper {
  flex: 1;
  overflow: auto;
  background-color: rgba($secondary-dark-blue, 0.5);
  border: 1px solid rgba($border-glow, 0.3);
  border-radius: 8px;
  box-shadow: 0 0 15px rgba($glow-blue, 0.2);
  padding: 10px;
}

.custom-table {
  --el-table-border-color: rgba(0, 136, 255, 0.3);
  --el-table-border: 1px solid var(--el-table-border-color);
  --el-table-text-color: #ffffff;
  --el-table-header-text-color: #00ffff;
  --el-table-row-hover-bg-color: rgba(0, 168, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
  width: 100%;
  font-size: 13px;
}
</style>

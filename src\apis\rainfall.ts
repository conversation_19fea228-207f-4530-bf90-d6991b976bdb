import httpInstance from '@/utils/http';

export function xinanjiangAPI(url: string,data: any): Promise<any> {
    return httpInstance({
        method: 'POST',
        url: url,
        data: data
    })
}
export function shanbeiAPI(url: string,data: any): Promise<any> {
    return httpInstance({
        method: 'POST',
        url: url,
        data: data
    })
}
export function nankeyuanAPI(url: string,data: any): Promise<any> {
    return httpInstance({
        method: 'POST',
        url: url,
        data: data
    })
}
export function downloadAPI(url: string): Promise<any> {
    return httpInstance({
        method: 'GET',
        url: url,
        responseType: "blob"
    })
}


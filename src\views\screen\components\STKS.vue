<template>
  <div class="stks-container">
    <div class="page-title">水位流量预测</div>

    <div class="content-container">
      <!-- 左侧地图区域 -->
      <div class="map-section">
        <div class="section-title">水文站点分布图</div>
        <div ref="chartDom" class="echarts-map-container"></div>
      </div>

      <!-- 右侧数据区域 -->
      <div class="data-section">
        <!-- 站点信息 -->
        <div class="station-info" v-if="selectedStation">
          <div class="info-title">{{ selectedStation.name }} 水文站</div>
          <div class="info-content">
            <div class="info-item">
              <span class="item-label">水文站点名称:</span>
              <span class="item-value">{{ selectedStation.userName }}</span>
            </div>
            <div class="info-item">
              <span class="item-label">下一时刻水位:</span>
              <span class="item-value">{{ currentWaterLevel }} 米</span>
            </div>
            <div class="info-item">
              <span class="item-label">下一时刻流量:</span>
              <span class="item-value">{{ currentFlow }} 立方米/秒</span>
            </div>
          </div>
        </div>
        <div class="station-info" v-else>
          <div class="info-title">请选择水文站点</div>
          <div class="info-content">
            <div class="info-item">点击地图上的水文站点查看详细信息</div>
          </div>
        </div>

        <!-- 表格区域 -->
        <div class="tables-container">
          <div class="table-wrapper">
            <div class="section-title">水位预测</div>
            <el-table
              :data="waterLevelTableData"
              class="custom-table"
              :header-cell-style="{backgroundColor: 'rgba(13, 34, 82, 0.8)', color: '#00ffff', fontWeight: 'bold', fontSize: '16px'}"
              :row-style="{backgroundColor: 'rgba(10, 26, 58, 0.6)'}"
              :cell-style="{color: '#ffffff', fontSize: '15px'}"
              size="small"
              height="160"
            >
              <el-table-column prop="time" label="时间" min-width="25%" align="center" />
              <el-table-column prop="forecastPeriod" label="预见期(h)" min-width="25%" align="center" />
              <el-table-column prop="level" label="水位(m)" min-width="25%" align="center" />
              <el-table-column prop="error" label="误差(m)" min-width="25%" align="center" />
              <el-table-column prop="status" label="状态" min-width="25%" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.status === '正常' ? 'success' : 'danger'" size="small">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="table-wrapper">
            <div class="section-title">流量预测</div>
            <el-table
              :data="flowTableData"
              class="custom-table"
              :header-cell-style="{backgroundColor: 'rgba(13, 34, 82, 0.8)', color: '#00ffff', fontWeight: 'bold', fontSize: '16px'}"
              :row-style="{backgroundColor: 'rgba(10, 26, 58, 0.6)'}"
              :cell-style="{color: '#ffffff', fontSize: '15px'}"
              size="small"
              height="160"
            >
              <el-table-column prop="time" label="时间" min-width="20%" align="center" />
              <el-table-column prop="forecastPeriod" label="预见期(h)" min-width="20%" align="center" />
              <el-table-column prop="flow" label="流量(m³/s)" min-width="20%" align="center" />
              <el-table-column prop="error" label="误差(m³/s)" min-width="20%" align="center" />
              <el-table-column prop="status" label="状态" min-width="20%" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.status === '正常' ? 'success' : 'danger'" size="small">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-container">
          <div class="chart-wrapper">
            <div class="section-title">水位过程图</div>
            <div ref="waterLevelChartDom" class="chart"></div>
          </div>

          <div class="chart-wrapper">
            <div class="section-title">流量过程图</div>
            <div ref="flowChartDom" class="chart"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, shallowRef, reactive } from 'vue';
import axios from 'axios';
// 引入 ECharts 核心模块
import * as echarts from 'echarts/core';
// 引入图表类型: Map, Scatter, Line
import { MapChart, ScatterChart, LineChart } from 'echarts/charts';
// 引入组件
import {
  TooltipComponent,
  GeoComponent,
  VisualMapComponent, // Optional
  LegendComponent
} from 'echarts/components';
// 引入渲染器
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  MapChart,
  ScatterChart,
  LineChart,
  TooltipComponent,
  GeoComponent,
  VisualMapComponent,
  LegendComponent,
  CanvasRenderer
]);



// --- 配置项 ---
// 1. River GeoJSON
const riverJsonPath = '/static/map/river.json'; // *** 确认路径正确 ***
// 2. Lake GeoJSON
const lakeJsonPath = '/static/map/lake.json';   // *** 确认路径正确 ***
// 3. Station Point Data
const stationDataPath = '/static/map/station.json'; // *** 确认路径正确 ***

// 4. 合并后地图的注册名称
const combinedMapName = 'CombinedRiverLakeMap';
// --- 配置项结束 ---

// 地图相关
const chartDom = ref(null);
const chartInstance = shallowRef(null);

// 水位图表相关
const waterLevelChartDom = ref(null);
const waterLevelChartInstance = shallowRef(null);

// 流量图表相关
const flowChartDom = ref(null);
const flowChartInstance = shallowRef(null);

// 站点选择状态
const selectedStation = ref(null);
const currentWaterLevel = ref('--');
const currentFlow = ref('--');

// 警戒水位和警戒流量数组（每个站点不同）
const warningLevels = {
  '宜昌': 44.5,
  '枝城': 47,
  '沙市': 38,
  '监利': 30,
  '城陵矶': 27,
  '螺山': 25,
  '石门': 67,
  '桃源': 67,
  '桃江': 67,
  '湘潭': 67,
  '默认': 65 // 默认警戒水位
};

const warningFlows = {
  '宜昌': 17000,
  '枝城': 17000,
  '沙市': 17000,
  '监利': 17000,
  '城陵矶': 6000,
  '螺山': 24000,
  '石门': 17000,
  '桃源': 6000,
  '桃江': 6000,
  '湘潭': 6000,
  '默认': 6000 // 默认警戒流量
};

// 固定预见期数组
const forecastPeriods = [6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 66, 72];

// 水位误差数组（每个站点的误差值）
const waterLevelErrors = {
  '宜昌': [0.14063, 0.16627, 0.17940, 0.19015, 0.22954, 0.25196, 0.26471, 0.28068, 0.31229, 0.33173, 0.34645, 0.35854],
  '枝城': [0.14063, 0.16627, 0.17940, 0.19015, 0.22954, 0.25196, 0.26471, 0.28068, 0.31229, 0.33173, 0.34645, 0.35854],
  '沙市': [0.14063, 0.16627, 0.17940, 0.19015, 0.22954, 0.25196, 0.26471, 0.28068, 0.31229, 0.33173, 0.34645, 0.35854],
  '监利': [0.14063, 0.16627, 0.17940, 0.19015, 0.22954, 0.25196, 0.26471, 0.28068, 0.31229, 0.33173, 0.34645, 0.35854],
  '城陵矶': [0.14063, 0.16627, 0.17940, 0.19015, 0.22954, 0.25196, 0.26471, 0.28068, 0.31229, 0.33173, 0.34645, 0.35854],
  '螺山': [0.14063, 0.16627, 0.17940, 0.19015, 0.22954, 0.25196, 0.26471, 0.28068, 0.31229, 0.33173, 0.34645, 0.35854],
  '石门': [0.14063, 0.16627, 0.17940, 0.19015, 0.22954, 0.25196, 0.26471, 0.28068, 0.31229, 0.33173, 0.34645, 0.35854],
  '桃源': [0.14063, 0.16627, 0.17940, 0.19015, 0.22954, 0.25196, 0.26471, 0.28068, 0.31229, 0.33173, 0.34645, 0.35854],
  '桃江': [0.14063, 0.16627, 0.17940, 0.19015, 0.22954, 0.25196, 0.26471, 0.28068, 0.31229, 0.33173, 0.34645, 0.35854],
  '湘潭': [0.14063, 0.16627, 0.17940, 0.19015, 0.22954, 0.25196, 0.26471, 0.28068, 0.31229, 0.33173, 0.34645, 0.35854],
  '默认': [0.14063, 0.16627, 0.17940, 0.19015, 0.22954, 0.25196, 0.26471, 0.28068, 0.31229, 0.33173, 0.34645, 0.35854],
};

// 流量误差数组（每个站点的误差值）
const flowErrors = {
  '宜昌': [188, 244, 293, 336, 412, 463, 510, 551, 614, 656, 689, 726],
  '枝城': [188, 244, 293, 336, 412, 463, 510, 551, 614, 656, 689, 726],
  '沙市': [188, 244, 293, 336, 412, 463, 510, 551, 614, 656, 689, 726],
  '监利': [188, 244, 293, 336, 412, 463, 510, 551, 614, 656, 689, 726],
  '城陵矶': [188, 244, 293, 336, 412, 463, 510, 551, 614, 656, 689, 726],
  '螺山': [188, 244, 293, 336, 412, 463, 510, 551, 614, 656, 689, 726],
  '石门': [188, 244, 293, 336, 412, 463, 510, 551, 614, 656, 689, 726],
  '桃源': [188, 244, 293, 336, 412, 463, 510, 551, 614, 656, 689, 726],
  '桃江': [188, 244, 293, 336, 412, 463, 510, 551, 614, 656, 689, 726],
  '湘潭': [188, 244, 293, 336, 412, 463, 510, 551, 614, 656, 689, 726],
  '默认': [188, 244, 293, 336, 412, 463, 510, 551, 614, 656, 689, 726]
};

// 表格数据
const waterLevelTableData = ref([]);
const flowTableData = ref([]);

// 图表数据
const waterLevelChartData = reactive({
  times: [],
  values: []
});

const flowChartData = reactive({
  times: [],
  values: []
});

// 初始化空数据
waterLevelTableData.value = [];
flowTableData.value = [];

let resizeObserver = null;

/**
 * 预处理 GeoJSON: 确保是 FeatureCollection 并有 name
 * (保持之前的 processGeoJson 函数不变)
 */
const processGeoJson = (geoJsonInput, defaultNamePrefix = '形状') => {
  // 检查是否是有效的 FeatureCollection 且已包含 name 属性
  if (geoJsonInput && geoJsonInput.type === 'FeatureCollection' && Array.isArray(geoJsonInput.features) && geoJsonInput.features.length > 0) {
      const hasName = geoJsonInput.features.every(f => f.properties?.name !== undefined && f.properties?.name !== null);
      if(hasName) {
          // console.log(`Input for '${defaultNamePrefix}' is FeatureCollection with names, using directly.`);
          return geoJsonInput;
      } else {
           // console.log(`Input for '${defaultNamePrefix}' is FeatureCollection but lacks names, adding default names...`);
           const features = geoJsonInput.features.map((feature, index) => ({
               ...feature,
                properties: {
                    ...feature.properties,
                    name: feature.properties?.name || `${defaultNamePrefix} ${index + 1}`
                },
           }));
           return { type: 'FeatureCollection', features: features };
      }
  }
  // 处理 GeometryCollection
  if (geoJsonInput && geoJsonInput.type === 'GeometryCollection' && Array.isArray(geoJsonInput.geometries)) {
    // console.log(`Processing GeometryCollection into FeatureCollection (${defaultNamePrefix})...`);
    const features = geoJsonInput.geometries.map((geometry, index) => ({
      type: 'Feature',
      properties: { name: `${defaultNamePrefix} ${index + 1}` },
      geometry: geometry
    }));
    return { type: 'FeatureCollection', features: features };
  }
  console.warn(`Input GeoJSON for '${defaultNamePrefix}' is not a recognized/processable format, attempting to use directly.`, geoJsonInput);
  return geoJsonInput; // 返回原始输入，让 ECharts 尝试处理
};


/**
 * 预处理 Station 数据: 提取点信息
 * (保持之前的 processStationData 函数不变)
 */
const processStationData = (stationRawData) => {
    if (!Array.isArray(stationRawData) || stationRawData.length === 0 || !stationRawData[0].children) {
        console.warn("Station data format is unexpected.", stationRawData);
        return [];
    }
    try {
        const stations = stationRawData[0].children;
        if (!Array.isArray(stations)) {
             console.warn("stationRawData[0].children is not an array.", stations);
             return [];
        }
        return stations.map(station => {
            if (!station.name || !station.coordinates || !Array.isArray(station.coordinates) || station.coordinates.length < 2) {
                console.warn("Skipping invalid station data:", station);
                return null;
            }
            return {
                name: station.name,
                value: [station.coordinates[0], station.coordinates[1]],
                userName: station.userName
            };
        }).filter(item => item !== null);
    } catch (error) {
        console.error("Error processing station data:", error);
        return [];
    }
};


// 初始化地图
const initChart = async () => {
  if (!chartDom.value) { console.error("DOM element not ready"); return; }

  try {
    // 1. 并行获取所有数据文件
    const [riverResponse, lakeResponse, stationResponse] = await Promise.all([
      fetch(riverJsonPath).catch(e => { console.error(`Fetch error for ${riverJsonPath}:`, e); return null; }),
      fetch(lakeJsonPath).catch(e => { console.error(`Fetch error for ${lakeJsonPath}:`, e); return null; }),
      fetch(stationDataPath).catch(e => { console.error(`Fetch error for ${stationDataPath}:`, e); return null; })
    ]);

    // 仔细检查响应
    if (!riverResponse || !riverResponse.ok) {
        throw new Error(`Failed to load River GeoJSON: ${riverResponse?.statusText || 'Fetch Error'} (Path: ${riverJsonPath})`);
    }
    if (!lakeResponse || !lakeResponse.ok) {
        throw new Error(`Failed to load Lake GeoJSON: ${lakeResponse?.statusText || 'Fetch Error'} (Path: ${lakeJsonPath})`);
    }
    if (!stationResponse || !stationResponse.ok) {
      throw new Error(`Failed to load Station data: ${stationResponse?.statusText || 'Fetch Error'} (Path: ${stationDataPath})`);
    }

    const [rawRiverData, rawLakeData, rawStationData] = await Promise.all([
        riverResponse.json(),
        lakeResponse.json(),
        stationResponse.json()
    ]);

    // 2. 预处理 River 和 Lake 数据
    const processedRiverJson = processGeoJson(rawRiverData, '河流');
    const processedLakeJson = processGeoJson(rawLakeData, '湖泊');

    // --- 关键步骤：合并 Feature ---
    const combinedFeatures = [];
    if (processedRiverJson && Array.isArray(processedRiverJson.features)) {
        combinedFeatures.push(...processedRiverJson.features);
    } else {
        console.warn("Processed River JSON does not contain a valid features array.");
    }
    if (processedLakeJson && Array.isArray(processedLakeJson.features)) {
        // 可选：给湖泊的 feature 添加一个特殊属性，如果以后想区分样式
        // processedLakeJson.features.forEach(f => f.properties = {...f.properties, featureType: 'lake'});
        combinedFeatures.push(...processedLakeJson.features);
    } else {
         console.warn("Processed Lake JSON does not contain a valid features array.");
    }

    if (combinedFeatures.length === 0) {
        throw new Error("No valid features found after processing and combining River and Lake JSON.");
    }

    const combinedGeoJson = {
        type: 'FeatureCollection',
        features: combinedFeatures
    };
    // --- 合并结束 ---

    // 3. 预处理 Station 数据
    const stationPointsData = processStationData(rawStationData);

    // console.log("--- Data Processing Results ---");
    // console.log("Combined GeoJSON for Map:", combinedGeoJson);
    // console.log("Processed Station Points:", stationPointsData);
    // console.log("--- End Data Processing Results ---");

    // 4. 注册合并后的地图数据
    echarts.registerMap(combinedMapName, combinedGeoJson);

    // 5. 初始化 ECharts 实例
    chartInstance.value = echarts.init(chartDom.value);

    // 6. 配置 ECharts 选项
    const options = {
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(13, 34, 82, 0.8)',
        borderColor: 'rgba(0, 168, 255, 0.8)',
        textStyle: {
          color: '#ffffff'
        }
      },
      // legend: { // 更新图例
      //     data: ['区域与湖泊', '水文站'], // 与 series 的 name 对应
      //     selectedMode: 'multiple',
      //     left: 'right',
      //     top: 'center',
      //     orient: 'vertical',
      //     textStyle: { color: '#333' }
      // },
      // 移除图例，直接在站点上显示名称
      // geo 组件作为底图和坐标系
      geo: {
        show: true,
        map: combinedMapName, // 使用合并后的地图
        roam: true,
        zoom: 1.2,
        center: [39750000, 3320000], // 调整地图中心点
        // 添加3D效果
        viewControl: {
          autoRotate: false,
          projection: 'perspective', // 透视投影，增强立体感
          distance: 120, // 视角距离
          alpha: 45, // 视角俯角角度，增大为45度
          beta: 10, // 视角旋转角度，稍微旋转
          minAlpha: 35, // 限制最小俯角
          maxAlpha: 55, // 限制最大俯角
          minBeta: -20, // 限制最小旋转角度
          maxBeta: 40, // 限制最大旋转角度
          damping: 0.8, // 滚动阻尼
          rotateSensitivity: 1.5, // 旋转灵敏度
        },
        label: { show: false },
        itemStyle: {
          areaColor: 'rgba(15, 40, 90, 0.7)', // 调整底图颜色为深蓝色
          borderColor: 'rgba(0, 198, 255, 0.85)', // 发光边缘
          borderWidth: 1.5,
          shadowColor: 'rgba(0, 198, 255, 0.6)', // 降低光晕亮度
          shadowBlur: 8,
          shadowOffsetX: 0,
          shadowOffsetY: 0
        },
      },
      series: [
        // Series 0: 合并后的地图层 (包含河流和湖泊)
         {
           name: '河流与湖泊', // 与 legend.data[0] 对应
           type: 'map',
           map: combinedMapName, // 使用注册的合并地图
           geoIndex: 0,        // 关联到 geo 组件
           tooltip: {
                trigger: 'item',
                backgroundColor: 'rgba(13, 34, 82, 0.8)',
                borderColor: 'rgba(0, 168, 255, 0.8)',
                textStyle: {
                  color: '#00ffff'
                },
                formatter: (params) => params.name || '地理要素'
           },
           itemStyle: {     // 统一的基础样式 (可以根据需要调整)
             areaColor: 'rgba(15, 40, 90, 0.7)', // 调整底图颜色为深蓝色
             borderColor: 'rgba(0, 198, 255, 0.85)', // 发光边缘
             borderWidth: 1.5,
             shadowColor: 'rgba(0, 198, 255, 0.6)', // 降低光晕亮度
             shadowBlur: 8,
             shadowOffsetX: 0,
             shadowOffsetY: 0
           },
           emphasis: {      // 统一的高亮样式
             focus: 'self',
             label: { show: false },
             itemStyle: {
               areaColor: 'rgba(0, 168, 255, 0.3)',
               borderColor: '#00ffff',
               borderWidth: 2,
               shadowColor: 'rgba(0, 198, 255, 0.8)',
               shadowBlur: 10
             }
           },
           data: [] // 如果区域不需要根据数值着色，data 为空
         },
         // Series 1: 水文站点层 (来自 station.json)
         {
            name: '水文站点',
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 0,
            data: stationPointsData,
            symbolSize: 25, // 适当调整图标大小
            symbol: 'pin', // 使用定位针形状
            symbolOffset: [0, 0], // 移除偏移，确保标记在正确位置
            label: {
                formatter: '{b}',
                position: 'right',
                show: true,
                fontSize: 18, // 调整文字大小，确保清晰
                color: '#ffffff',
                textShadow: '0 0 5px rgba(0,0,0,0.8)',
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                padding: [5, 8], // 增大内边距
                borderRadius: 4,
                distance: 10 // 增大文字与图标的距离
            },
            itemStyle: {
                color: '#ff3333', // 水文站点改为红色
                borderColor: '#ffffff',
                borderWidth: 1,
                shadowColor: 'rgba(255, 51, 51, 0.8)', // 光晕也改为红色
                shadowBlur: 10
            },
            emphasis: {
                focus: 'self',
                scale: true,
                scaleSize: 2,
                label: {
                    show: true,
                    color: '#00ffff',
                    fontSize: 29, // 增大高亮时的文字大小
                    fontWeight: 'bold'
                },
                itemStyle: {
                    color: '#ff0000', // 高亮时为更亮的红色
                    borderColor: '#ffffff',
                    borderWidth: 2,
                    shadowBlur: 15,
                    shadowColor: 'rgba(255, 0, 0, 1)' // 高亮时的光晕也改为红色
                }
            },
            tooltip: { // 保持之前的站点 tooltip
                formatter: (params) => {
                    const data = params.data;
                    if (!data) return '';
                    const coords = data.value ? data.value.map(c => c.toFixed(2)).join(', ') : 'N/A';
                    return `${params.marker} ${data.name || '未知站点'}<br/>
                            坐标: ${coords}<br/>
                            名称: ${data.userName || 'N/A'}`;
                }
            },
            // 添加点击事件
            zlevel: 5 // 确保水文站点显示在最上层
         }
      ]
    };

    // 7. 应用配置项
    chartInstance.value.setOption(options, true);

    // 8. 添加点击事件监听
    chartInstance.value.off('click'); // 先移除之前的点击事件监听器
    chartInstance.value.on('click', 'series.scatter', handleStationClick);

  } catch (error) {
    console.error('初始化 ECharts 地图失败:', error);
    if (chartDom.value) {
        chartDom.value.innerText = `加载地图失败: ${error.message}. 请检查浏览器控制台获取详细信息。`;
    }
  }
};

// 从后端获取水位预测数据
const fetchWaterLevelPrediction = async () => {
  try {
    const response = await axios.get('/api/levelprediction/');
    return response.data;
  } catch (error) {
    console.error('获取水位预测数据失败:', error);
    return null;
  }
};

// 从后端获取流量预测数据
const fetchFlowPrediction = async () => {
  try {
    const response = await axios.get('/api/flowprediction/');
    return response.data;
  } catch (error) {
    console.error('获取流量预测数据失败:', error);
    return null;
  }
};

// 处理站点点击事件
const handleStationClick = async (params) => {
  const stationData = params.data;
  if (!stationData) return;

  // 更新选中的站点信息
  selectedStation.value = stationData;

  // 获取水位和流量预测数据
  await fetchAndProcessData();

  // 初始化或更新图表
  initWaterLevelChart();
  initFlowChart();
};

// 处理水位预测数据
const processWaterLevelData = (data) => {
  if (!data || !data.data || !data.data.predictions || !data.data.timestamps) {
    console.error('水位预测数据格式不正确');
    return;
  }

  const { predictions, timestamps } = data.data;

  // 获取当前站点在数组中的索引
  const stationIndex = getStationIndex(selectedStation.value?.name);
  if (stationIndex === -1) {
    console.warn('找不到站点对应的数据索引');
    return;
  }

  // 设置当前水位（使用第一个预测值）
  if (predictions.length > 0 && predictions[0].length > stationIndex) {
    currentWaterLevel.value = predictions[0][stationIndex].toFixed(2);
  }

  // 清空现有数据
  waterLevelTableData.value = [];
  waterLevelChartData.times = [];
  waterLevelChartData.values = [];

  // 获取最后一个时间戳作为基准时间
  const lastTimestamp = timestamps[timestamps.length - 1];
  const baseTime = new Date(lastTimestamp);
  // console.log('最后一个时间戳:', lastTimestamp, '基准时间:', baseTime);
  baseTime.setHours(baseTime.getHours() - 8);
  // 获取当前站点的误差数组
  const stationName = selectedStation.value?.name || '默认';
  const errorArray = waterLevelErrors[stationName] || waterLevelErrors['默认'];

  // 处理每个预测时间点
  for (let i = 0; i < predictions.length && i < forecastPeriods.length; i++) {
    // 添加预见期小时数
    const forecastPeriod = forecastPeriods[i];
    // 使用最后一个时间戳作为基准，然后以固定间隔递增
    // 每行依次递增6个小时
    const forecastTime = new Date(baseTime.getTime() + (i * 6 + 6) * 60 * 60 * 1000);

    // 格式化时间显示（月/日 时:分）
    const month = forecastTime.getMonth() + 1;
    const day = forecastTime.getDate();
    const hours = forecastTime.getHours().toString().padStart(2, '0');
    const minutes = forecastTime.getMinutes().toString().padStart(2, '0');
    const displayTime = `${month}/${day} ${hours}:${minutes}`;
    // console.log('预测时间:', displayTime);

    // 获取当前站点的水位值
    const level = predictions[i][stationIndex].toFixed(2);

    // 获取当前站点的警戒水位
    const warningLevel = warningLevels[stationName] || warningLevels['默认'];

    // 判断水位状态
    const status = parseFloat(level) > warningLevel ? '警戒' : '正常';

    // 使用预定义的误差值
    const error = errorArray[i].toFixed(2);

    // 添加到表格数据
    waterLevelTableData.value.push({
      time: displayTime,
      forecastPeriod,
      level,
      error,
      status
    });

    // 添加到图表数据
    waterLevelChartData.times.push(displayTime);
    waterLevelChartData.values.push(parseFloat(level));
  }
};

// 处理流量预测数据
const processFlowData = (data) => {
  if (!data || !data.data || !data.data.predictions || !data.data.timestamps) {
    console.error('流量预测数据格式不正确');
    return;
  }

  const { predictions, timestamps } = data.data;

  // 获取当前站点在数组中的索引
  const stationIndex = getStationIndex(selectedStation.value?.name);
  if (stationIndex === -1) {
    console.warn('找不到站点对应的数据索引');
    return;
  }

  // 设置当前流量（使用第一个预测值）
  if (predictions.length > 0 && predictions[0].length > stationIndex) {
    // 保留三位有效数字，避免科学计数法
    const flowValue = predictions[0][stationIndex];
    currentFlow.value = parseFloat(Number(flowValue).toPrecision(3)).toLocaleString('zh-CN', {
      useGrouping: false,
      maximumFractionDigits: 20
    });
  }

  // 清空现有数据
  flowTableData.value = [];
  flowChartData.times = [];
  flowChartData.values = [];

  // 获取最后一个时间戳作为基准时间
  const lastTimestamp = timestamps[timestamps.length - 1];
  const baseTime = new Date(lastTimestamp);
  // console.log('流量最后一个时间戳:', lastTimestamp, '基准时间:', baseTime);

  // 获取当前站点的误差数组
  const stationName = selectedStation.value?.name || '默认';
  const errorArray = flowErrors[stationName] || flowErrors['默认'];

  // 处理每个预测时间点
  for (let i = 0; i < predictions.length && i < forecastPeriods.length; i++) {
    // 添加预见期小时数
    const forecastPeriod = forecastPeriods[i];
    // 使用最后一个时间戳作为基准，然后以固定间隔递增
    // 每行依次递增6个小时
    const forecastTime = new Date(baseTime.getTime() + (i * 6 + 6) * 60 * 60 * 1000);

    // 格式化时间显示（月/日 时:分）
    const month = forecastTime.getMonth() + 1;
    const day = forecastTime.getDate();
    const hours = forecastTime.getHours().toString().padStart(2, '0');
    const minutes = forecastTime.getMinutes().toString().padStart(2, '0');
    const displayTime = `${month}/${day} ${hours}:${minutes}`;

    // 获取当前站点的流量值
    const flowValue = predictions[i][stationIndex];
    // 保留原始值用于图表显示
    const flowForChart = Math.round(flowValue);
    // 格式化为三位有效数字用于表格显示，避免科学计数法
    const flow = parseFloat(Number(flowValue).toPrecision(3)).toLocaleString('zh-CN', {
      useGrouping: false,
      maximumFractionDigits: 20
    });

    // 获取当前站点的警戒流量
    const warningFlow = warningFlows[stationName] || warningFlows['默认'];

    // 判断流量状态 - 使用原始流量值进行比较
    const status = flowValue > warningFlow ? '警戒' : '正常';

    // 使用预定义的误差值
    // const error = errorArray[i].toFixed(2);
    const error = errorArray[i].toFixed(0);

    // 添加到表格数据
    flowTableData.value.push({
      time: displayTime,
      forecastPeriod,
      flow,
      error,
      status
    });

    // 添加到图表数据
    flowChartData.times.push(displayTime);
    flowChartData.values.push(flowForChart);
  }
};

// 获取站点在数组中的索引
const getStationIndex = (stationName) => {
  if (!stationName) return -1;

  // 根据 warningLevels 中的站点顺序确定索引
  const stationNames = Object.keys(warningLevels).filter(name => name !== '默认');
  return stationNames.indexOf(stationName);
};

// 获取并处理水位和流量预测数据
const fetchAndProcessData = async () => {
  // 获取水位和流量预测数据
  const [waterLevelData, flowData] = await Promise.all([
    fetchWaterLevelPrediction(),
    fetchFlowPrediction()
  ]);

  // 如果获取数据失败，显示错误信息
  if (!waterLevelData || !flowData) {
    console.error('获取预测数据失败');
    return;
  }

  // 处理水位预测数据
  processWaterLevelData(waterLevelData);

  // 处理流量预测数据
  processFlowData(flowData);
};



// 初始化水位图表
const initWaterLevelChart = () => {
  if (!waterLevelChartDom.value) return;

  if (!waterLevelChartInstance.value) {
    waterLevelChartInstance.value = echarts.init(waterLevelChartDom.value, 'dark-blue');
  }

  const option = {
    grid: {
      top: 40, // 增加顶部空间，防止水位标题被截断
      right: 100, // 显著增加右侧空间，确保警戒水位标签完全显示
      bottom: 30,
      left: 60, // 增加左侧空间，保证Y轴标签显示完整
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(13, 34, 82, 0.8)',
      borderColor: 'rgba(0, 168, 255, 0.8)',
      textStyle: {
        color: '#ffffff'
      }
    },
    xAxis: {
      type: 'category',
      data: waterLevelChartData.times,
      axisLabel: {
        interval: 2,
        color: '#ffffff',
        fontSize: 14
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 168, 255, 0.5)'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '水位(米)',
      nameTextStyle: {
        color: '#00ffff',
        fontSize: 16
      },
      axisLabel: {
        color: '#ffffff',
        fontSize: 14
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 168, 255, 0.2)'
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 168, 255, 0.5)'
        }
      },
      // 设置为true，使坐标轴不从0开始，而是根据数据范围自动调整
      scale: true,
      // 设置上下留白，使图表更加清晰
      minInterval: 0.1, // 最小刻度间隔
      splitNumber: 5 // 坐标轴分割段数
    },
    series: [{
      name: '水位',
      type: 'line',
      data: waterLevelChartData.values,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#00a8ff'
      },
      lineStyle: {
        width: 3,
        color: '#00a8ff'
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(0, 168, 255, 0.5)' },
          { offset: 1, color: 'rgba(0, 168, 255, 0.1)' }
        ])
      },
      markLine: {
        silent: true,
        lineStyle: {
          color: '#ff4d4f',
          width: 2,
          type: 'solid'
        },
        symbol: ['none', 'none'], // 不显示线两端的标记
        label: {
          position: 'insideEndTop', // 标签放在线的右端上方
          formatter: () => {
            const level = selectedStation.value ?
              (warningLevels[selectedStation.value.name] || warningLevels['默认']) :
              warningLevels['默认'];
            return `警戒: ${level}m`; // 简化标签文本
          },
          fontSize: 13, // 减小字体大小
          color: '#ff4d4f',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          padding: [2, 4],
          distance: 8, // 减小标签与线的距离
          align: 'left' // 文本左对齐
        },
        data: [{
          yAxis: selectedStation.value ?
            (warningLevels[selectedStation.value.name] || warningLevels['默认']) :
            warningLevels['默认'],
          name: '警戒水位'
        }]
      }
    }]
  };

  waterLevelChartInstance.value.setOption(option);
};

// 初始化流量图表
const initFlowChart = () => {
  if (!flowChartDom.value) return;

  if (!flowChartInstance.value) {
    flowChartInstance.value = echarts.init(flowChartDom.value, 'dark-blue');
  }

  const option = {
    grid: {
      top: 40, // 增加顶部空间，防止流量标题被截断
      right: 100, // 显著增加右侧空间，确保警戒流量标签完全显示
      bottom: 30,
      left: 60, // 增加左侧空间，保证Y轴标签显示完整
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(13, 34, 82, 0.8)',
      borderColor: 'rgba(0, 168, 255, 0.8)',
      textStyle: {
        color: '#ffffff'
      }
    },
    xAxis: {
      type: 'category',
      data: flowChartData.times,
      axisLabel: {
        interval: 2,
        color: '#ffffff',
        fontSize: 14
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 168, 255, 0.5)'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '流量(立方米/秒)',
      nameTextStyle: {
        color: '#00ffff',
        fontSize: 16
      },
      axisLabel: {
        color: '#ffffff',
        fontSize: 14
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 168, 255, 0.2)'
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 168, 255, 0.5)'
        }
      },
      // 设置为true，使坐标轴不从0开始，而是根据数据范围自动调整
      scale: true,
      // 设置上下留白，使图表更加清晰
      minInterval: 100, // 最小刻度间隔
      splitNumber: 5 // 坐标轴分割段数
    },
    series: [{
      name: '流量',
      type: 'line',
      data: flowChartData.values,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#00ffff'
      },
      lineStyle: {
        width: 3,
        color: '#00ffff'
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(0, 255, 255, 0.5)' },
          { offset: 1, color: 'rgba(0, 255, 255, 0.1)' }
        ])
      },
      markLine: {
        silent: true,
        lineStyle: {
          color: '#ff4d4f',
          width: 2,
          type: 'solid'
        },
        symbol: ['none', 'none'], // 不显示线两端的标记
        label: {
          position: 'insideEndTop', // 标签放在线的右端上方
          formatter: () => {
            const flow = selectedStation.value ?
              (warningFlows[selectedStation.value.name] || warningFlows['默认']) :
              warningFlows['默认'];
            return `警戒: ${flow}m³/s`; // 简化标签文本
          },
          fontSize: 13, // 减小字体大小
          color: '#ff4d4f',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          padding: [2, 4],
          distance: 8, // 减小标签与线的距离
          align: 'left' // 文本左对齐
        },
        data: [{
          yAxis: selectedStation.value ?
            (warningFlows[selectedStation.value.name] || warningFlows['默认']) :
            warningFlows['默认'],
          name: '警戒流量'
        }]
      }
    }]
  };

  flowChartInstance.value.setOption(option);
};

// --- 生命周期钩子和 Resize 逻辑 ---
const resizeCharts = () => {
  if (chartInstance.value) { chartInstance.value.resize(); }
  if (waterLevelChartInstance.value) { waterLevelChartInstance.value.resize(); }
  if (flowChartInstance.value) { flowChartInstance.value.resize(); }
};

onMounted(async () => {
  // 初始化地图
  await initChart();

  // 设置窗口大小变化监听
  if (chartDom.value) {
    resizeObserver = new ResizeObserver(resizeCharts);
    resizeObserver.observe(chartDom.value);
  }

  // 如果有默认选中的站点，获取数据
  if (selectedStation.value) {
    await fetchAndProcessData();
    initWaterLevelChart();
    initFlowChart();
  }
});

onBeforeUnmount(() => {
  // 清理资源
  if (resizeObserver && chartDom.value) {
    resizeObserver.unobserve(chartDom.value);
  }

  // 销毁图表实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
    chartInstance.value = null;
  }

  if (waterLevelChartInstance.value) {
    waterLevelChartInstance.value.dispose();
    waterLevelChartInstance.value = null;
  }

  if (flowChartInstance.value) {
    flowChartInstance.value.dispose();
    flowChartInstance.value = null;
  }
});

</script>

<style lang="scss" scoped>
// Main colors
$primary-dark-blue: #0a1a3a;
$secondary-dark-blue: #0d2252;
$highlight-blue: #00a8ff;
$glow-blue: #00c6ff;
$text-light: #ffffff;
$text-highlight: #00ffff;
$border-glow: #0088ff;
$map-highlight: #00a8ff;
$data-highlight: #00ffff;
$warning-color: #ff4d4f;
$success-color: #52c41a;

.stks-container {
  width: 100%;
  height: 100vh;
  padding: 0; // 移除内边距，使地图占据更多空间
  background-color: #0a1a35; // 调整为深蓝色背景，不那么黑
  background-image:
    linear-gradient(to bottom, rgba(15, 35, 70, 0.85), rgba(10, 26, 53, 0.9)),
    url('/static/img/grid-pattern.png');
  background-size: cover, 20px 20px;
  color: $text-light;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(0, 198, 255, 0.06) 0%, rgba(10, 26, 53, 0) 60%);
    pointer-events: none;
    z-index: 1;
  }
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: $text-highlight;
  text-shadow: 0 0 10px rgba($glow-blue, 0.5);
  margin-bottom: 20px;
  text-align: center;
  width: 100%;
  z-index: 2;
  position: relative;
}

.content-container {
  display: flex;
  flex: 1;
  gap: 20px;
  position: relative;
  z-index: 2;
  height: calc(100vh - 80px); // 设置内容区域高度，留出页面标题的空间
}

.map-section {
  width: 38%; // 缩小地图区域宽度
  min-width: 400px;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba($border-glow, 0.5);
  border-radius: 5px;
  background-color: rgba($secondary-dark-blue, 0.3);
  box-shadow: 0 0 15px rgba($glow-blue, 0.3), inset 0 0 30px rgba(0, 198, 255, 0.1);
  overflow: hidden;
}

.data-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow: auto; // 允许数据区域滚动，防止内容溢出
}

.section-title {
  font-size: 22px;
  font-weight: bold;
  color: $text-highlight;
  text-shadow: 0 0 5px rgba($glow-blue, 0.5);
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid rgba($border-glow, 0.3);
}

.echarts-map-container {
  width: 100%;
  flex: 1;
  border: none; // 移除边框
  border-radius: 0; // 移除圆角
  background-color: transparent;
  position: relative;
  z-index: 2;
  box-shadow: inset 0 0 80px rgba(0, 198, 255, 0.15);
}

.station-info {
  border: 1px solid rgba($border-glow, 0.5);
  border-radius: 5px;
  background-color: rgba($secondary-dark-blue, 0.3);
  box-shadow: 0 0 15px rgba($glow-blue, 0.3);
  overflow: hidden;
  height: 165px;
}

.info-title {
  font-size: 22px;
  font-weight: bold;
  color: $text-highlight;
  text-shadow: 0 0 5px rgba($glow-blue, 0.5);
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid rgba($border-glow, 0.3);
}

.info-content {
  padding: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 18px;

  .item-label {
    color: rgba($text-light, 0.8);
  }

  .item-value {
    color: $data-highlight;
    font-weight: bold;
    font-family: 'Consolas', 'Courier New', monospace; /* 使用本地等宽字体替代Share Tech Mono */
  }
}

.tables-container {
  display: flex;
  gap: 15px;
  height: 215px;
}

.table-wrapper {
  flex: 1;
  border: 1px solid rgba($border-glow, 0.5);
  border-radius: 5px;
  background-color: rgba($secondary-dark-blue, 0.3);
  box-shadow: 0 0 15px rgba($glow-blue, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.charts-container {
  display: flex;
  gap: 15px;
  flex: 1;
  min-height: 280px; // 增加图表容器的最小高度，确保有足够空间显示警戒线和标签
}

.chart-wrapper {
  flex: 1;
  border: 1px solid rgba($border-glow, 0.5);
  border-radius: 5px;
  background-color: rgba($secondary-dark-blue, 0.3);
  box-shadow: 0 0 15px rgba($glow-blue, 0.3);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chart {
  flex: 1;
  padding: 10px 0; // 添加上下内边距，给图表内容更多空间
}

// 自定义表格样式
:deep(.custom-table) {
  --el-table-border-color: rgba(0, 136, 255, 0.3);
  --el-table-border: 1px solid var(--el-table-border-color);
  --el-table-text-color: #ffffff;
  --el-table-header-text-color: #00ffff;
  --el-table-row-hover-bg-color: rgba(0, 168, 255, 0.1);
  background-color: transparent;

  .el-table__inner-wrapper::before {
    display: none;
  }

  .el-table__header {
    background-color: rgba(13, 34, 82, 0.8);
  }

  .el-table__row {
    background-color: rgba(10, 26, 58, 0.6);
  }

  .el-table__cell {
    border-bottom: 1px solid rgba(0, 136, 255, 0.3);
  }
}

// 自定义标签样式
:deep(.el-tag--success) {
  background-color: rgba($success-color, 0.2);
  border-color: rgba($success-color, 0.5);
  color: $success-color;
}

:deep(.el-tag--danger) {
  background-color: rgba($warning-color, 0.2);
  border-color: rgba($warning-color, 0.5);
  color: $warning-color;
}
</style>
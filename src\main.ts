

import { createApp } from 'vue'
import { createPinia } from 'pinia'
// 初始化样式
import '@/styles/common.scss'
// 引入全局样式
import '@/styles/global.scss'
// 引入新的深蓝色主题
import '@/styles/theme-dark-blue.scss'
// 引入字体文件
import './assets/font/iconfont.css';

import App from './App.vue'
import router from './router'
import { useUserStore } from './stores/userStore'
// import axios from 'axios'
// import 'element-plus/theme-chalk/dark/css-vars.css' // 引入element-plus的暗黑主题样式


// import 'element-plus/es/locale/lang/zh-cn'
// import ElementPlus from 'element-plus'
// import 'element-plus/dist/index.css'
// import zhCn from 'element-plus/es/locale/lang/zh-cn';
// import 'element-plus/theme-chalk/dark/css-vars.css'
//Particles
// import Particles from 'particles.vue3'

// import SocketService from '@/utils/socket_service';
// 对服务端进行websocket的连接
// SocketService.Instance.connect()  //get方法不需要小括号


//请求基准路径配置————————————————————————————————后端url
// axios.defaults.baseURL = 'http://127.0.0.1:8888/api/'
// axios.defaults.baseURL = 'http://localhost:8080/'


// 引入 echarts
import * as echarts from 'echarts';
// 引入深蓝色主题
import './assets/theme/dark-blue.js';

const app = createApp(App)

//将axios挂载到vue原型对象上
// app.config.globalProperties.$http = axios

// app.config.globalProperties.$chalk = chalk

app.config.globalProperties.$echarts = echarts

// 创建Pinia实例
const pinia = createPinia()
app.use(pinia)

// 加载用户信息
const userStore = useUserStore()
userStore.loadUserInfo()

app.use(router)
// app.use(ElementPlus, {size: 'small', zIndex: 3000, locale: zhCn}) //size: 'small', zIndex: 3000,

// app.use(Particles)
app.mount('#app')

点对

<template>
  <div class="sange-container">
    <div ref="chartDom" class="echarts-map-container"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, shallowRef } from 'vue';
// 引入 ECharts 核心模块
import * as echarts from 'echarts/core';
// 引入图表类型: Map, Scatter, Lines
import { MapChart, ScatterChart, LinesChart } from 'echarts/charts';
// 引入组件
import {
  TooltipComponent,
  GeoComponent,
  VisualMapComponent,
  LegendComponent
} from 'echarts/components';
// 引入渲染器
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  MapChart,
  ScatterChart,
  LinesChart,
  TooltipComponent,
  GeoComponent,
  VisualMapComponent,
  LegendComponent,
  CanvasRenderer
]);

// --- 配置项 ---
// 1. GeoJSON 文件路径
const hruJsonPath = '/static/map/Hru单元.geojson';
const sectionJsonPath = '/static/map/断面.geojson';
const riverSystemJsonPath = '/static/map/水系.geojson';
const boundaryJsonPath = '/static/map/外边界范围.geojson';
const stationJsonPath = '/static/map/水文站.geojson';

// 2. 图层和坐标系注册名称
const geoCoordSysName = 'mainGeoSystem';
const layerNames = {
  combinedArea: 'combinedAreaLayer', // 合并的Hru单元和外边界范围
  river: 'riverLayer',
  section: 'sectionLayer'
};

const chartDom = ref(null);
const chartInstance = shallowRef(null);
let resizeObserver = null;

/**
 * 处理水文站点数据
 */
const processStationData = (stationGeoJson) => {
  if (!stationGeoJson || stationGeoJson.type !== 'FeatureCollection' || !Array.isArray(stationGeoJson.features)) {
    console.warn("水文站点数据格式不正确", stationGeoJson);
    return [];
  }

  try {
    return stationGeoJson.features.map(feature => {
      if (feature.type !== 'Feature' || !feature.geometry || feature.geometry.type !== 'Point') {
        console.warn("跳过无效的站点数据:", feature);
        return null;
      }

      const coordinates = feature.geometry.coordinates;
      const properties = feature.properties || {};

      return {
        name: properties.Name || '未命名站点',
        value: coordinates,
        // 保存其他属性以便在tooltip中显示
        ...properties
      };
    }).filter(item => item !== null);
  } catch (error) {
    console.error("处理水文站点数据时出错:", error);
    return [];
  }
};

/**
 * 合并GeoJSON数据处理函数 - 确保坐标系一致性
 */
const mergeGeoData = (baseGeoJson, ...otherGeoJsons) => {
  if (!baseGeoJson) return null;

  // 创建基础GeoJSON的深拷贝
  const mergedGeoJson = JSON.parse(JSON.stringify(baseGeoJson));

  // 确保features数组存在
  if (!mergedGeoJson.features) {
    mergedGeoJson.features = [];
  }

  // 合并其他GeoJSON的features
  otherGeoJsons.forEach(geoJson => {
    if (geoJson && geoJson.features && Array.isArray(geoJson.features)) {
      // 将每个feature添加到合并的GeoJSON中
      geoJson.features.forEach(feature => {
        if (feature) {
          // 添加额外的属性，标记来源
          feature.properties = feature.properties || {};
          feature.properties._source = geoJson.name || 'unknown';
          mergedGeoJson.features.push(feature);
        }
      });
    }
  });

  return mergedGeoJson;
};

/**
 * 将GeoJSON中的LineString或MultiLineString转换为ECharts Lines所需的格式
 */
const convertGeoJSONToLines = (features) => {
  if (!features || !Array.isArray(features)) {
    return [];
  }

  const linesData = [];

  features.forEach(feature => {
    if (!feature.geometry) return;

    const properties = feature.properties || {};
    const name = properties.Name || properties.name || '未命名线段';

    // 处理LineString类型
    if (feature.geometry.type === 'LineString' && Array.isArray(feature.geometry.coordinates)) {
      linesData.push({
        name: name,
        coords: feature.geometry.coordinates,
        properties: properties
      });
    }
    // 处理MultiLineString类型
    else if (feature.geometry.type === 'MultiLineString' && Array.isArray(feature.geometry.coordinates)) {
      feature.geometry.coordinates.forEach((lineCoords, index) => {
        linesData.push({
          name: `${name}-${index + 1}`,
          coords: lineCoords,
          properties: properties
        });
      });
    }
  });

  return linesData;
};

// 格式化通用tooltip显示
// 注意：现在我们在每个图层中单独定义了tooltip格式化函数

// 格式化站点tooltip
const formatStationTooltip = (params) => {
  const data = params.data || {};

  let content = `<div style="font-weight:bold;margin-bottom:5px;">${data.name || '未知站点'}</div>`;

  // 添加坐标信息
  if (data.value) {
    const coords = Array.isArray(data.value) ? data.value.map(c => c.toFixed(4)).join(', ') : 'N/A';
    content += `坐标: ${coords}<br/>`;
  }

  // 添加其他属性信息
  Object.entries(data).forEach(([key, value]) => {
    if (!['name', 'value', 'symbolSize', 'itemStyle', 'label'].includes(key) &&
        value !== undefined && value !== null) {
      content += `${key}: ${value}<br/>`;
    }
  });

  return content;
};

const initChart = async () => {
  if (!chartDom.value) {
    console.error("DOM element not ready");
    return;
  }

  console.log('开始初始化地图...');

  try {
    // 并行获取所有数据文件
    const [
      boundaryResponse,
      hruResponse,
      riverSystemResponse,
      sectionResponse,
      stationResponse
    ] = await Promise.all([
      fetch(boundaryJsonPath).catch(e => {
        console.error(`Fetch error for ${boundaryJsonPath}:`, e);
        return null;
      }),
      fetch(hruJsonPath).catch(e => {
        console.error(`Fetch error for ${hruJsonPath}:`, e);
        return null;
      }),
      fetch(riverSystemJsonPath).catch(e => {
        console.error(`Fetch error for ${riverSystemJsonPath}:`, e);
        return null;
      }),
      fetch(sectionJsonPath).catch(e => {
        console.error(`Fetch error for ${sectionJsonPath}:`, e);
        return null;
      }),
      fetch(stationJsonPath).catch(e => {
        console.error(`Fetch error for ${stationJsonPath}:`, e);
        return null;
      })
    ]);

    // 解析各个图层的GeoJSON数据
    const boundaryData = boundaryResponse?.ok ? await boundaryResponse.json() : null;
    const hruData = hruResponse?.ok ? await hruResponse.json() : null;
    const riverSystemData = riverSystemResponse?.ok ? await riverSystemResponse.json() : null;
    const sectionData = sectionResponse?.ok ? await sectionResponse.json() : null;
    const stationData = stationResponse?.ok ? await stationResponse.json() : null;



    // 3. 合并Hru单元和外边界范围
    let combinedAreaData = null;
    if (hruData && boundaryData) {
      // 合并Hru单元和外边界范围
      combinedAreaData = mergeGeoData(hruData, boundaryData);
      echarts.registerMap(layerNames.combinedArea, combinedAreaData);
      console.log('注册合并区域图层:', combinedAreaData.features?.length || 0, '个要素');
    } else if (hruData) {
      // 只有Hru单元
      combinedAreaData = hruData;
      echarts.registerMap(layerNames.combinedArea, combinedAreaData);
      console.log('注册区域图层(仅Hru单元):', combinedAreaData.features?.length || 0, '个要素');
    } else if (boundaryData) {
      // 只有外边界范围
      combinedAreaData = boundaryData;
      echarts.registerMap(layerNames.combinedArea, combinedAreaData);
      console.log('注册区域图层(仅外边界范围):', combinedAreaData.features?.length || 0, '个要素');
    }

    // --- 重要改变: 使用共享的地理坐标系 ---

    // 1. 确定基础地图 (优先使用边界数据)
    // const baseGeoJson = hruData || riverSystemData || boundaryData || sectionData;
    const baseGeoJson = combinedAreaData;

    if (!baseGeoJson) {
      throw new Error('无法加载任何地图数据，请检查数据文件和路径');
    }

    // 2. 注册主地理坐标系统
    echarts.registerMap(geoCoordSysName, baseGeoJson);
    console.log('已注册主地理坐标系统');

    // 注册水系和断面图层
    if (riverSystemData) {
      echarts.registerMap(layerNames.river, riverSystemData);
      console.log('注册水系图层:', riverSystemData.features?.length || 0, '个要素');
    }

    if (sectionData) {
      echarts.registerMap(layerNames.section, sectionData);
      console.log('注册断面图层:', sectionData.features?.length || 0, '个要素');
    }

    // 处理站点数据
    const stationPointsData = stationData ? processStationData(stationData) : [];
    console.log('处理水文站点数据:', stationPointsData.length, '个站点');

    // 初始化 ECharts 实例
    chartInstance.value = echarts.init(chartDom.value);

    // 准备各图层数据
    // 将水系和断面转换为lines格式
    const riverSystemLinesData = riverSystemData ? convertGeoJSONToLines(riverSystemData.features) : [];
    const sectionLinesData = sectionData ? convertGeoJSONToLines(sectionData.features) : [];

    // 构建系列配置
    const series = [];

    // 设置底图 geo 组件 (作为坐标系统)
    const geoComponent = {
      show: true,
      map: geoCoordSysName,
      roam: true,
      zoom: 1.2,
      // 添加3D效果
      viewControl: {
        autoRotate: false,
        projection: 'perspective', // 透视投影，增强立体感
        distance: 120, // 视角距离
        alpha: 45, // 视角俯角角度，增大为45度
        beta: 10, // 视角旋转角度，稍微旋转
        minAlpha: 35, // 限制最小俯角
        maxAlpha: 55, // 限制最大俯角
        minBeta: -20, // 限制最小旋转角度
        maxBeta: 40, // 限制最大旋转角度
        damping: 0.8, // 滚动阻尼
        rotateSensitivity: 1.5, // 旋转灵敏度
      },
      label: {
        show: false
      },
      itemStyle: {
        areaColor: 'rgba(15, 40, 90, 0.7)', // 调整底图颜色为深蓝色
        borderColor: 'rgba(0, 198, 255, 0.85)', // 发光边缘
        borderWidth: 1.5,
        shadowColor: 'rgba(0, 198, 255, 0.6)', // 降低光晕亮度
        shadowBlur: 8,
        shadowOffsetX: 0,
        shadowOffsetY: 0
      },
      regionHeight: 5, // 区域高度，增强立体感
      emphasis: {
        label: {
          show: false
        },
        itemStyle: {
          areaColor: 'rgba(10, 30, 80, 0.8)',
          borderColor: 'rgba(0, 255, 255, 1)',
          borderWidth: 2,
          shadowColor: 'rgba(0, 255, 255, 1)',
          shadowBlur: 15
        }
      },
      light: { // 添加光照效果
        main: {
          color: '#fff',
          intensity: 1.5,
          shadow: true,
          shadowQuality: 'high',
          alpha: 45, // 与视角俯角一致
          beta: 30
        },
        ambient: {
          color: '#0066ff',
          intensity: 0.4
        },
        ambientCubemap: {
          texture: '/static/img/grid-pattern.png',
          exposure: 1,
          diffuseIntensity: 0.5,
          specularIntensity: 1
        }
      },
      postEffect: {
        enable: true,
        bloom: {
          enable: true,
          bloomIntensity: 0.2 // 发光强度
        },
        depthOfField: {
          enable: true,
          focalDistance: 100,
          focalRange: 100,
          blurRadius: 5
        }
      },
      temporalSuperSampling: {
        enable: true
      }
    };

    // 添加合并的区域图层 (Hru单元和外边界范围)
    // if (combinedAreaData) {
    //   series.push({
    //     name: '区域范围',
    //     type: 'map',
    //     map: layerNames.combinedArea,
    //     roam: false,
    //     zoom: 1.2,
    //     tooltip: {
    //       formatter: params => formatTooltip(params, '区域范围')
    //     },
    //     itemStyle: {
    //       areaColor: 'rgba(65, 105, 225, 0.5)', // 半透明皇家蓝
    //       borderColor: 'rgba(0, 0, 255, 0.6)',
    //       borderWidth: 1
    //     },
    //     emphasis: {
    //       itemStyle: {
    //         areaColor: 'rgba(100, 149, 237, 0.7)', // 半透明矢车菊蓝
    //         borderColor: '#00ffff',
    //         borderWidth: 2
    //       }
    //     },
    //     data: combinedAreaMapData,
    //     zlevel: 1
    //   });
    // }

    // 添加水系图层 (如果有) - 使用lines类型渲染
    if (riverSystemLinesData.length > 0) {
      series.push({
        name: '水系',
        type: 'lines',
        coordinateSystem: 'geo', // 使用geo坐标系
        polyline: true, // 支持多段线
        effect: { // 添加流动效果
          show: true,
          period: 5,
          trailLength: 0.5,
          symbol: 'none',
          symbolSize: 4,
          color: '#00ffa0', // 流动效果颜色也改为浅绿色
          constantSpeed: 35
        },
        data: riverSystemLinesData.map(line => ({
          name: line.name,
          coords: line.coords,
          // 保存属性信息便于tooltip显示
          properties: line.properties
        })),
        lineStyle: {
          color: 'rgba(0, 255, 170, 0.9)', // 水系线颜色改为浅绿色
          width: 2.5,
          opacity: 0.85,
          curveness: 0.1,
          shadowColor: 'rgba(0, 255, 170, 0.5)', // 降低光晕了度
          shadowBlur: 8 // 降低发光范围
        },
        emphasis: {
          lineStyle: {
            color: '#00ffa0',
            width: 3,
            opacity: 0.95,
            shadowColor: 'rgba(0, 255, 170, 0.7)',
            shadowBlur: 10
          }
        },
        tooltip: {
          formatter: params => {
            const data = params.data || {};
            const properties = data.properties || {};
            let content = `<div style="font-weight:bold;margin-bottom:5px;">${params.name || '水系'}</div>`;
            Object.entries(properties).forEach(([key, value]) => {
              if (key !== 'name' && value !== undefined && value !== null) {
                content += `${key}: ${value}<br/>`;
              }
            });
            return content;
          }
        },
        zlevel: 2
      });
    }

    // 添加断面图层 (如果有) - 使用lines类型渲染
    if (sectionLinesData.length > 0) {
      series.push({
        name: '断面',
        type: 'lines',
        coordinateSystem: 'geo', // 使用geo坐标系
        polyline: true, // 支持多段线
        data: sectionLinesData.map(line => ({
          name: line.name,
          coords: line.coords,
          // 保存属性信息便于tooltip显示
          properties: line.properties
        })),
        lineStyle: {
          color: 'rgba(255, 165, 0, 0.9)', // 断面线颜色
          width: 3,
          opacity: 0.9,
          shadowColor: 'rgba(255, 165, 0, 0.8)',
          shadowBlur: 12
        },
        emphasis: {
          lineStyle: {
            color: '#ffff00',
            width: 3,
            opacity: 1,
            shadowColor: 'rgba(255, 255, 0, 1)',
            shadowBlur: 12
          }
        },
        tooltip: {
          formatter: params => {
            const data = params.data || {};
            const properties = data.properties || {};
            let content = `<div style="font-weight:bold;margin-bottom:5px;">${params.name || '断面'}</div>`;
            Object.entries(properties).forEach(([key, value]) => {
              if (key !== 'name' && value !== undefined && value !== null) {
                content += `${key}: ${value}<br/>`;
              }
            });
            return content;
          }
        },
        zlevel: 3
      });
    }

    // 添加水文站点图层 (如果有)
    if (stationPointsData.length > 0) {
      series.push({
        name: '水文站点',
        type: 'scatter',
        coordinateSystem: 'geo', // 使用geo坐标系
        data: stationPointsData,
        symbolSize: 35, // 增大图标大小
        symbol: 'pin', // 使用定位针形状
        // symbolOffset: [0, '-50%'], // 向上偏移，增强3D效果
        symbolOffset: [0, 0], // 向上偏移，增强3D效果
        label: {
          formatter: '{b}',
          position: 'right',
          show: true,
          fontSize: 25, // 增大文字大小
          color: '#ffffff',
          textShadow: '0 0 5px rgba(0,0,0,0.8)',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          padding: [5, 8], // 增大内边距
          borderRadius: 4,
          distance: 10 // 增大文字与图标的距离
        },
        itemStyle: {
          color: '#ff3333', // 水文站点改为红色
          borderColor: '#ffffff',
          borderWidth: 1,
          shadowColor: 'rgba(255, 51, 51, 0.8)', // 光晕也改为红色
          shadowBlur: 10
        },
        emphasis: {
          focus: 'self',
          scale: true,
          scaleSize: 2,
          label: {
            show: true,
            color: '#00ffff',
            fontSize: 29, // 增大高亮时的文字大小
            fontWeight: 'bold'
          },
          itemStyle: {
            color: '#ff0000', // 高亮时为更亮的红色
            borderColor: '#ffffff',
            borderWidth: 2,
            shadowBlur: 15,
            shadowColor: 'rgba(255, 0, 0, 1)' // 高亮时的光晕也改为红色
          }
        },
        tooltip: {
          formatter: formatStationTooltip
        },
        zlevel: 5
      });
    }

    // 配置 ECharts 选项
    const options = {
      backgroundColor: 'rgba(10, 26, 53, 0.9)', // 调整为深蓝色背景
      layoutCenter: ['50%', '-150%'],//位置
	    layoutSize:'460%',//大小
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(10, 26, 53, 0.9)',
        borderColor: 'rgba(0, 198, 255, 0.8)',
        borderWidth: 1,
        textStyle: {
          color: '#ffffff'
        },
        shadowColor: 'rgba(0, 198, 255, 0.5)',
        shadowBlur: 10
      },
      legend: {
        // 添加图例控制图层显示/隐藏
        orient: 'vertical',
        right: '7%',
        top: 'center',
        data: series.map(s => s.name),
        textStyle: {
          color: '#ffffff',
          fontSize: 25 // 增大图例文字大小
        },
        itemGap: 20, // 增大图例项目间距
        itemWidth: 30, // 增大图例图标宽度
        itemHeight: 20, // 增大图例图标高度
        itemStyle: {
          borderColor: 'rgba(0, 198, 255, 0.8)'
        },
        selected: series.reduce((acc, s) => {
          acc[s.name] = true;
          return acc;
        }, {})
      },
      // 添加全局光照效果
      aria: {
        enabled: true,
        decal: {
          show: true
        }
      },
      geo: geoComponent,
      series: series
    };

    // 应用配置项
    chartInstance.value.setOption(options, true);

    console.log('ECharts 实例初始化成功');
    console.log('添加的图层数:', series.length);

  } catch (error) {
    console.error('初始化 ECharts 地图失败:', error);
    if (chartDom.value) {
      chartDom.value.innerText = `加载地图失败: ${error.message}. 请检查浏览器控制台获取详细信息。`;
    }
  }
};

// --- 生命周期钩子和 Resize 逻辑 ---
const resizeChart = () => {
  if (chartInstance.value) { chartInstance.value.resize(); }
};

onMounted(() => {
  initChart();
  if (chartDom.value) {
    resizeObserver = new ResizeObserver(resizeChart);
    resizeObserver.observe(chartDom.value);
  }
});

onBeforeUnmount(() => {
  if (resizeObserver && chartDom.value) { resizeObserver.unobserve(chartDom.value); }
  if (chartInstance.value) { chartInstance.value.dispose(); chartInstance.value = null; }
});
</script>

<style lang="scss" scoped>
// Main colors
$primary-dark-blue: #0a1a3a;
$secondary-dark-blue: #0d2252;
$highlight-blue: #00a8ff;
$glow-blue: #00c6ff;
$text-light: #ffffff;
$text-highlight: #00ffff;
$border-glow: #0088ff;
$map-highlight: #00a8ff;
$data-highlight: #00ffff;

.sange-container {
  width: 100%;
  height: 100vh;
  padding: 0; // 移除内边距，使地图占据全屏
  background-color: #0a1a35; // 调整为深蓝色背景，不那么黑
  background-image:
    linear-gradient(to bottom, rgba(15, 35, 70, 0.85), rgba(10, 26, 53, 0.9)),
    url('/static/img/grid-pattern.png');
  background-size: cover, 20px 20px;
  color: $text-light;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(0, 198, 255, 0.06) 0%, rgba(10, 26, 53, 0) 60%);
    pointer-events: none;
    z-index: 1;
  }

  // 噪点纹理效果可以后续添加
  // &::after {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   bottom: 0;
  //   background-image: url('/static/img/noise-pattern.png');
  //   background-size: 200px 200px;
  //   opacity: 0.03;
  //   pointer-events: none;
  //   z-index: 1;
  // }
}

.echarts-map-container {
  width: 100%;
  flex: 1;
  border: none; // 移除边框
  border-radius: 0; // 移除圆角
  background-color: transparent;
  position: relative;
  z-index: 2;
  box-shadow: inset 0 0 80px rgba(0, 198, 255, 0.15);
}
</style>
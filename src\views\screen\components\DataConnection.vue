<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  startPoint: {
    type: Object,
    required: true,
    validator: (value: any) => {
      return typeof value.x === 'number' && typeof value.y === 'number';
    }
  },
  endPoint: {
    type: Object,
    required: true,
    validator: (value: any) => {
      return typeof value.x === 'number' && typeof value.y === 'number';
    }
  },
  color: {
    type: String,
    default: 'rgba(0, 198, 255, 0.7)'
  },
  pulseColor: {
    type: String,
    default: 'rgba(0, 255, 255, 0.9)'
  },
  lineWidth: {
    type: Number,
    default: 2
  }
});

const canvasRef = ref<HTMLCanvasElement | null>(null);
let animationFrameId: number | null = null;
let progress = 0;

// 绘制连接线
function drawConnection() {
  if (!canvasRef.value) return;
  
  const canvas = canvasRef.value;
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  
  // 绘制基础线
  ctx.beginPath();
  ctx.strokeStyle = props.color;
  ctx.lineWidth = props.lineWidth;
  ctx.moveTo(props.startPoint.x, props.startPoint.y);
  ctx.lineTo(props.endPoint.x, props.endPoint.y);
  ctx.stroke();
  
  // 计算当前点的位置
  const currentX = props.startPoint.x + (props.endPoint.x - props.startPoint.x) * progress;
  const currentY = props.startPoint.y + (props.endPoint.y - props.startPoint.y) * progress;
  
  // 绘制移动的点
  ctx.beginPath();
  ctx.fillStyle = props.pulseColor;
  ctx.arc(currentX, currentY, props.lineWidth * 2, 0, Math.PI * 2);
  ctx.fill();
  
  // 绘制光晕效果
  ctx.beginPath();
  const gradient = ctx.createRadialGradient(
    currentX, currentY, props.lineWidth,
    currentX, currentY, props.lineWidth * 4
  );
  gradient.addColorStop(0, props.pulseColor);
  gradient.addColorStop(1, 'transparent');
  ctx.fillStyle = gradient;
  ctx.arc(currentX, currentY, props.lineWidth * 4, 0, Math.PI * 2);
  ctx.fill();
  
  // 更新进度
  progress += 0.01;
  if (progress > 1) {
    progress = 0;
  }
  
  // 继续动画
  animationFrameId = requestAnimationFrame(drawConnection);
}

// 调整canvas大小
function resizeCanvas() {
  if (!canvasRef.value) return;
  
  const container = canvasRef.value.parentElement;
  if (!container) return;
  
  canvasRef.value.width = container.offsetWidth;
  canvasRef.value.height = container.offsetHeight;
}

onMounted(() => {
  resizeCanvas();
  drawConnection();
  window.addEventListener('resize', resizeCanvas);
});

onUnmounted(() => {
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId);
  }
  window.removeEventListener('resize', resizeCanvas);
});
</script>

<template>
  <canvas ref="canvasRef" class="data-connection-canvas"></canvas>
</template>

<style scoped>
.data-connection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}
</style>

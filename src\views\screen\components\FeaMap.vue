<template>
    <div ref="chartDom" class="echarts-map-container"></div>
  </template>
  
  <script setup>
  import { ref, onMounted, onBeforeUnmount, shallowRef } from 'vue';
  // 引入 ECharts 核心模块，核心模块提供了 ECharts 使用必须要的接口。
  import * as echarts from 'echarts/core';
  // 引入地图图表，图表后缀都为 Chart
  import { MapChart } from 'echarts/charts';
  // 引入提示框，标题，视觉映射等组件，组件后缀都为 Component
  import {
    TooltipComponent,
    VisualMapComponent,
    GeoComponent // 如果需要更复杂的地理坐标系配置，可能需要引入 GeoComponent
  } from 'echarts/components';
  // 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
  import { CanvasRenderer } from 'echarts/renderers';
  // 如果需要 SVG 渲染器，可以替换为
  // import { SVGRenderer } from 'echarts/renderers';
  
  // 注册必须的组件
  echarts.use([
    MapChart,
    TooltipComponent,
    VisualMapComponent,
    GeoComponent,
    CanvasRenderer // 或者 SVGRenderer
  ]);
  
  // --- 配置项 ---
  // 1. GeoJSON 文件路径 (相对于 public 目录)
  // *** 修改这里为你 GeoJSON 文件的实际路径 ***
  const geoJsonPath = '/static/map/huangnizhuang.json'; // 例如: '/china.geojson' 或 '/your_region.geojson'
  
  // 2. 注册的地图名称 (可以自定义，但需要与 series.map 保持一致)
  const mapName = 'MyCustomMap';
  // --- 配置项结束 ---
  
  // 用于挂载 ECharts 图表的 DOM 元素
  const chartDom = ref(null);
  // ECharts 实例。使用 shallowRef 避免 Vue 对 ECharts 内部复杂对象进行深度代理
  const chartInstance = shallowRef(null);
  // 用于 resize 的函数引用
  let resizeObserver = null;
  
  // 初始化图表函数
  const initChart = async () => {
    if (!chartDom.value) {
      console.error("DOM 元素未准备好");
      return;
    }
  
    try {
      // 1. 获取 GeoJSON 数据
      const response = await fetch(geoJsonPath);
      if (!response.ok) {
        throw new Error(`无法加载 GeoJSON 文件: ${response.statusText} (路径: ${geoJsonPath})`);
      }
      const geoJson = await response.json();
  
      // 2. 注册地图
      echarts.registerMap(mapName, geoJson);
  
      // 3. 初始化 ECharts 实例
      chartInstance.value = echarts.init(chartDom.value);
  
      // 4. 配置 ECharts 选项
      const options = {
        tooltip: {
          trigger: 'item', // 触发类型，'item' 表示数据项图形触发
          formatter: '{b}<br/>{c}' // 提示框浮层内容格式器，{b} 表示数据名，{c} 表示数据值 (如果 series.data 里有 value 的话)
          // 如果你的 series.data 中没有 value，只想显示地名，可以只用 '{b}'
          // formatter: '{b}'
        },
        // 如果需要根据数值范围显示不同颜色，可以配置 visualMap
        visualMap: {
          min: 0,         // *** 根据你的实际数据调整范围 ***
          max: 1000,      // *** 根据你的实际数据调整范围 ***
          left: 'left',
          top: 'bottom',
          text: ['高', '低'], // 两端的文本
          calculable: true, // 是否显示拖拽用的手柄
          inRange: {
            // *** 自定义颜色范围 ***
            color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695'].reverse()
          },
          show: true // *** 如果暂时没有数据驱动颜色，可以设置为 false ***
        },
        series: [
          {
            name: '地图数据', // 系列名称，用于 tooltip 的显示
            type: 'map',    // 图表类型：地图
            map: mapName,   // 使用已注册的地图名称
            roam: true,     // 开启鼠标缩放和平移漫游
            label: {
              show: true,  // 是否显示地名标签
              fontSize: 10,
              color: '#333'
            },
            itemStyle: {    // 地图区域的多边形 图形样式
              areaColor: '#DDEEFF', // 地图区域的颜色 (会被 visualMap 覆盖，如果 visualMap 生效)
              borderColor: '#FFFFFF', // 图形的描边颜色
              borderWidth: 0.5        // 描边线宽
            },
            emphasis: {     // 高亮状态下的多边形和标签样式
              label: {
                color: '#000'
              },
              itemStyle: {
                areaColor: '#FFD700', // 高亮区域颜色
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            data: [] // 初始为空，你可以从 API 获取数据后填充这里，然后再次 setOption
          }
        ]
      };
  
      // 5. 应用配置项
      chartInstance.value.setOption(options);
  
    } catch (error) {
      console.error('初始化 ECharts 地图失败:', error);
      // 可以在界面上显示错误信息
      if (chartDom.value) {
          chartDom.value.innerText = `加载地图失败: ${error.message}`;
      }
    }
  };
  
  // 处理图表大小自适应
  const resizeChart = () => {
    if (chartInstance.value) {
      chartInstance.value.resize();
    }
  };
  
  // 组件挂载后执行
  onMounted(() => {
    initChart();
    // 使用 ResizeObserver 监听容器大小变化，比监听 window resize 更精确
    if (chartDom.value) {
      resizeObserver = new ResizeObserver(resizeChart);
      resizeObserver.observe(chartDom.value);
    }
    // 也可以用 window resize，但不推荐用于独立组件
    // window.addEventListener('resize', resizeChart);
  });
  
  // 组件卸载前执行
  onBeforeUnmount(() => {
    // 停止监听 resize
    if (resizeObserver && chartDom.value) {
      resizeObserver.unobserve(chartDom.value);
    }
    // window.removeEventListener('resize', resizeChart);
  
    // 销毁 ECharts 实例
    if (chartInstance.value) {
      chartInstance.value.dispose();
      chartInstance.value = null; // 显式设为 null
    }
  });
  
  </script>
  
  <style scoped>
  .echarts-map-container {
    width: 100%;
    /* *** 设置一个合适的高度 *** */
    height: 600px;
    border: 1px solid #ccc; /* 可选：加个边框方便查看 */
  }
  </style>
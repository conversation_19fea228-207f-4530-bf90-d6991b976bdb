<template>
    <el-upload ref="upload1" class="upload-demo" style="text-align: center;" :on-change="changeFileRain"
        action="#" :limit="1" :on-exceed="handleExceed1"
        :auto-upload="false">
        <template #trigger>
            <el-button type="primary" size="large">雨量资料</el-button>
        </template>
        <!-- <el-button class="ml-3" type="success" @click="submitUpload1" size="large">
            确认上传
        </el-button> -->
        <template #tip>
            <div class="el-upload__tip text-red" style="font-size: 15px;">
                请输入雨量资料
            </div>
        </template>
    </el-upload>
    <br>
    <el-upload ref="upload2" class="upload-demo" style="text-align: center;" :on-change="changeFileParameter"
        action="#" :limit="1" :on-exceed="handleExceed2"
        :auto-upload="false">
        <template #trigger>
            <el-button type="primary" size="large">参数文件</el-button>
        </template>
        <!-- <el-button class="ml-3" type="success" @click="submitUpload2" size="large">
            确认上传
        </el-button> -->
        <template #tip>
            <div class="el-upload__tip text-red" style="font-size: 15px;">
                请输入对应模型的参数文件
            </div>
        </template>
    </el-upload>
    <!-- <br>
    <el-upload ref="upload3" class="upload-demo" style="text-align: center;"
        action="#" :limit="1" :on-exceed="handleExceed3"
        :auto-upload="false">
        <template #trigger>
            <el-button type="primary" size="large">流量资料</el-button>
        </template>
        <template #tip>
            <div class="el-upload__tip text-red" style="font-size: 15px;">
                可选项：输入实测流量用于对比
            </div>
        </template>
    </el-upload> -->
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { genFileId } from 'element-plus'
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import {useUploadStore} from '@/stores/uploadStore';
const uploadStore = useUploadStore()
// console.log(uploadStore.fileRain)

const upload1 = ref<UploadInstance>()
const upload2 = ref<UploadInstance>()
const upload3 = ref<UploadInstance>()

// const file = ref()
const changeFileRain = (uploadFile: any) => {
    uploadStore.fileRain = uploadFile;
    // console.log(uploadStore.fileRain)
}
const changeFileParameter = (uploadFile: any) => {
    uploadStore.fileParameter = uploadFile;
    // console.log(uploadStore.fileParameter)
}

const handleExceed1: UploadProps['onExceed'] = (files) => {
    upload1.value!.clearFiles()
    const file1 = files[0] as UploadRawFile
    file1.uid = genFileId()
    upload1.value!.handleStart(file1)
}
const handleExceed2: UploadProps['onExceed'] = (files) => {
    upload2.value!.clearFiles()
    const file2 = files[0] as UploadRawFile
    file2.uid = genFileId()
    upload2.value!.handleStart(file2)
}
const handleExceed3: UploadProps['onExceed'] = (files) => {
    upload3.value!.clearFiles()
    const file3 = files[0] as UploadRawFile
    file3.uid = genFileId()
    upload3.value!.handleStart(file3)
}

// const rainFiles = upload1.value.uploadFiles
//   const paramFiles = upload2.value.uploadFiles
//   const flowFiles = upload3.value.uploadFiles

//   // 在这里处理文件数据
//   console.log('上传的雨量文件:', rainFiles)
//   console.log('上传的参数文件:', paramFiles)
//   console.log('上传的流量文件:', flowFiles)

defineExpose({
upload1,
upload2,
upload3
})

// const submitUpload1 = () => {
//     upload1.value!.submit()
// }
// const submitUpload2 = () => {
//     upload2.value!.submit()
// }
// const submitUpload3 = () => {
//     upload3.value!.submit()
// }
</script>
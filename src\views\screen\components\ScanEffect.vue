<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  color: {
    type: String,
    default: 'rgba(0, 198, 255, 0.3)'
  },
  duration: {
    type: Number,
    default: 4000
  }
});

const scanLineRef = ref<HTMLDivElement | null>(null);
let animationInterval: number | null = null;

// 启动扫描动画
function startScanAnimation() {
  if (!scanLineRef.value) return;
  
  // 重置位置
  scanLineRef.value.style.top = '-100%';
  
  // 设置动画
  scanLineRef.value.style.animation = `scanLine ${props.duration / 1000}s linear infinite`;
}

onMounted(() => {
  startScanAnimation();
});

onUnmounted(() => {
  if (animationInterval !== null) {
    clearInterval(animationInterval);
  }
});
</script>

<template>
  <div class="scan-container">
    <div ref="scanLineRef" class="scan-line" :style="{ 'background': `linear-gradient(to bottom, transparent, ${color}, transparent)` }"></div>
    <slot></slot>
  </div>
</template>

<style scoped>
.scan-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.scan-line {
  position: absolute;
  top: -100%;
  left: 0;
  width: 100%;
  height: 150px;
  pointer-events: none;
  z-index: 2;
}

@keyframes scanLine {
  0% {
    top: -10%;
  }
  100% {
    top: 100%;
  }
}
</style>

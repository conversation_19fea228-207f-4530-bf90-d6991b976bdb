import axios from 'axios'
import { ElMessage } from 'element-plus'
import 'element-plus/theme-chalk/el-message.css'
import router from '@/router'

// 创建axios实例
const httpInstance = axios.create({
    baseURL: 'http://localhost:8080',
    // baseURL: 'http://***************:8080',
    // baseURL: 'http://***************:8080',
    // baseURL: '/api',
    timeout: 5000 // 请求超时时间
})

// axios请求拦截器
httpInstance.interceptors.request.use(config => {
    // 从localStorage获取token数据
    const userInfoStr = localStorage.getItem('userInfo')
    if (userInfoStr) {
        const userInfo = JSON.parse(userInfoStr)
        const token = userInfo.token
        const tokenType = userInfo.token_type || 'Bearer'
        if (token) {
            // 按照OAuth2规范，添加Bearer token到Authorization头
            config.headers.Authorization = `${tokenType} ${token}`
        }
    }
    return config
}, e => Promise.reject(e))

// axios响应式拦截器
httpInstance.interceptors.response.use(res => res.data, e => {
    // 统一错误提示
    ElMessage({
        type: 'warning',
        message: e.response?.data?.message || '请求失败'
    })

    // 401 代表token失效，需要重新登录
    if (e.response && e.response.status === 401) {
        // 清除用户信息
        localStorage.removeItem('userInfo')
        // 跳转到登录页
        router.push('/login')
    }
    return Promise.reject(e)
})


export default httpInstance
import * as echarts from 'echarts';
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['exports', 'echarts'], factory);
    } else if (typeof exports === 'object' && typeof exports.nodeName !== 'string') {
        // CommonJS
        factory(exports, require('echarts'));
    } else {
        // Browser globals
        factory({}, echarts);
    }
}(this, function (exports, echarts) {
    var log = function (msg) {
        if (typeof console !== 'undefined') {
            console && console.error && console.error(msg);
        }
    };
    if (!echarts) {
        log('ECharts is not loaded');
        return;
    }
    echarts.registerTheme('dark-blue', {
        "color": [
            "#00c6ff",
            "#0088ff",
            "#00ffff",
            "#00a8ff",
            "#36d1dc",
            "#5b86e5",
            "#00d2ff",
            "#00b4db"
        ],
        "backgroundColor": "rgba(10, 26, 58, 0.8)",
        "textStyle": {
            "color": "#ffffff"
        },
        "title": {
            "textStyle": {
                "color": "#00ffff"
            },
            "subtextStyle": {
                "color": "rgba(255, 255, 255, 0.8)"
            }
        },
        "line": {
            "itemStyle": {
                "borderWidth": 1
            },
            "lineStyle": {
                "width": 2
            },
            "symbolSize": 4,
            "symbol": "circle",
            "smooth": true
        },
        "radar": {
            "itemStyle": {
                "borderWidth": 1
            },
            "lineStyle": {
                "width": 2
            },
            "symbolSize": 4,
            "symbol": "circle",
            "smooth": true
        },
        "bar": {
            "itemStyle": {
                "barBorderWidth": 0,
                "barBorderColor": "#ccc"
            }
        },
        "pie": {
            "itemStyle": {
                "borderWidth": 0,
                "borderColor": "#ccc"
            }
        },
        "scatter": {
            "itemStyle": {
                "borderWidth": 0,
                "borderColor": "#ccc"
            }
        },
        "boxplot": {
            "itemStyle": {
                "borderWidth": 0,
                "borderColor": "#ccc"
            }
        },
        "parallel": {
            "itemStyle": {
                "borderWidth": 0,
                "borderColor": "#ccc"
            }
        },
        "sankey": {
            "itemStyle": {
                "borderWidth": 0,
                "borderColor": "#ccc"
            }
        },
        "funnel": {
            "itemStyle": {
                "borderWidth": 0,
                "borderColor": "#ccc"
            }
        },
        "gauge": {
            "itemStyle": {
                "borderWidth": 0,
                "borderColor": "#ccc"
            },
            "axisLine": {
                "lineStyle": {
                    "color": [[0.2, "#00c6ff"], [0.8, "#0088ff"], [1, "#00ffff"]]
                }
            },
            "axisTick": {
                "length": 10,
                "lineStyle": {
                    "color": "auto"
                }
            },
            "splitLine": {
                "length": 15,
                "lineStyle": {
                    "color": "auto"
                }
            },
            "pointer": {
                "length": "80%",
                "width": 3,
                "color": "#00ffff"
            },
            "title": {
                "color": "#00ffff"
            },
            "detail": {
                "color": "#00ffff"
            }
        },
        "candlestick": {
            "itemStyle": {
                "color": "#00c6ff",
                "color0": "#0088ff",
                "borderColor": "#00c6ff",
                "borderColor0": "#0088ff",
                "borderWidth": 1
            }
        },
        "graph": {
            "itemStyle": {
                "borderWidth": 0,
                "borderColor": "#ccc"
            },
            "lineStyle": {
                "width": 1,
                "color": "#aaa"
            },
            "symbolSize": 4,
            "symbol": "circle",
            "smooth": true,
            "color": [
                "#00c6ff",
                "#0088ff",
                "#00ffff",
                "#00a8ff",
                "#36d1dc",
                "#5b86e5"
            ],
            "label": {
                "color": "#ffffff"
            }
        },
        "map": {
            "itemStyle": {
                "areaColor": "#0d2252",
                "borderColor": "#00c6ff",
                "borderWidth": 0.5
            },
            "label": {
                "color": "#ffffff"
            },
            "emphasis": {
                "itemStyle": {
                    "areaColor": "#0088ff",
                    "borderColor": "#00ffff",
                    "borderWidth": 1
                },
                "label": {
                    "color": "#ffffff"
                }
            }
        },
        "geo": {
            "itemStyle": {
                "areaColor": "#0d2252",
                "borderColor": "#00c6ff",
                "borderWidth": 0.5
            },
            "label": {
                "color": "#ffffff"
            },
            "emphasis": {
                "itemStyle": {
                    "areaColor": "#0088ff",
                    "borderColor": "#00ffff",
                    "borderWidth": 1
                },
                "label": {
                    "color": "#ffffff"
                }
            }
        },
        "categoryAxis": {
            "axisLine": {
                "show": true,
                "lineStyle": {
                    "color": "rgba(255, 255, 255, 0.3)"
                }
            },
            "axisTick": {
                "show": true,
                "lineStyle": {
                    "color": "rgba(255, 255, 255, 0.3)"
                }
            },
            "axisLabel": {
                "show": true,
                "color": "rgba(255, 255, 255, 0.8)"
            },
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "color": [
                        "rgba(255, 255, 255, 0.1)"
                    ]
                }
            },
            "splitArea": {
                "show": false,
                "areaStyle": {
                    "color": [
                        "rgba(250,250,250,0.05)",
                        "rgba(200,200,200,0.02)"
                    ]
                }
            }
        },
        "valueAxis": {
            "axisLine": {
                "show": true,
                "lineStyle": {
                    "color": "rgba(255, 255, 255, 0.3)"
                }
            },
            "axisTick": {
                "show": true,
                "lineStyle": {
                    "color": "rgba(255, 255, 255, 0.3)"
                }
            },
            "axisLabel": {
                "show": true,
                "color": "rgba(255, 255, 255, 0.8)"
            },
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "color": [
                        "rgba(255, 255, 255, 0.1)"
                    ]
                }
            },
            "splitArea": {
                "show": false,
                "areaStyle": {
                    "color": [
                        "rgba(250,250,250,0.05)",
                        "rgba(200,200,200,0.02)"
                    ]
                }
            }
        },
        "logAxis": {
            "axisLine": {
                "show": true,
                "lineStyle": {
                    "color": "rgba(255, 255, 255, 0.3)"
                }
            },
            "axisTick": {
                "show": true,
                "lineStyle": {
                    "color": "rgba(255, 255, 255, 0.3)"
                }
            },
            "axisLabel": {
                "show": true,
                "color": "rgba(255, 255, 255, 0.8)"
            },
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "color": [
                        "rgba(255, 255, 255, 0.1)"
                    ]
                }
            },
            "splitArea": {
                "show": false,
                "areaStyle": {
                    "color": [
                        "rgba(250,250,250,0.05)",
                        "rgba(200,200,200,0.02)"
                    ]
                }
            }
        },
        "timeAxis": {
            "axisLine": {
                "show": true,
                "lineStyle": {
                    "color": "rgba(255, 255, 255, 0.3)"
                }
            },
            "axisTick": {
                "show": true,
                "lineStyle": {
                    "color": "rgba(255, 255, 255, 0.3)"
                }
            },
            "axisLabel": {
                "show": true,
                "color": "rgba(255, 255, 255, 0.8)"
            },
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "color": [
                        "rgba(255, 255, 255, 0.1)"
                    ]
                }
            },
            "splitArea": {
                "show": false,
                "areaStyle": {
                    "color": [
                        "rgba(250,250,250,0.05)",
                        "rgba(200,200,200,0.02)"
                    ]
                }
            }
        },
        "toolbox": {
            "iconStyle": {
                "borderColor": "#00ffff"
            },
            "emphasis": {
                "iconStyle": {
                    "borderColor": "#00c6ff"
                }
            }
        },
        "legend": {
            "textStyle": {
                "color": "rgba(255, 255, 255, 0.8)"
            }
        },
        "tooltip": {
            "axisPointer": {
                "lineStyle": {
                    "color": "rgba(255, 255, 255, 0.3)",
                    "width": 1
                },
                "crossStyle": {
                    "color": "rgba(255, 255, 255, 0.3)",
                    "width": 1
                }
            }
        },
        "timeline": {
            "lineStyle": {
                "color": "#00c6ff",
                "width": 1
            },
            "itemStyle": {
                "color": "#00c6ff",
                "borderWidth": 1
            },
            "controlStyle": {
                "color": "#00c6ff",
                "borderColor": "#00c6ff",
                "borderWidth": 0.5
            },
            "checkpointStyle": {
                "color": "#00ffff",
                "borderColor": "#00ffff"
            },
            "label": {
                "color": "#00c6ff"
            },
            "emphasis": {
                "itemStyle": {
                    "color": "#00ffff"
                },
                "controlStyle": {
                    "color": "#00c6ff",
                    "borderColor": "#00c6ff",
                    "borderWidth": 0.5
                },
                "label": {
                    "color": "#00ffff"
                }
            }
        },
        "visualMap": {
            "color": [
                "#00c6ff",
                "#0088ff",
                "#00ffff"
            ]
        },
        "dataZoom": {
            "backgroundColor": "rgba(10, 26, 58, 0.4)",
            "dataBackgroundColor": "rgba(255, 255, 255, 0.1)",
            "fillerColor": "rgba(0, 198, 255, 0.2)",
            "handleColor": "#00c6ff",
            "handleSize": "100%",
            "textStyle": {
                "color": "rgba(255, 255, 255, 0.8)"
            }
        },
        "markPoint": {
            "label": {
                "color": "#ffffff"
            },
            "emphasis": {
                "label": {
                    "color": "#ffffff"
                }
            }
        }
    });
}));
